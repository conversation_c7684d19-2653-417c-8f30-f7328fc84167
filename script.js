// Show/Hide Content Sections
function hideAllSections() {
    console.log('إخفاء جميع الأقسام');

    const sections = document.querySelectorAll('.content-section');
    console.log('عدد الأقسام:', sections.length);

    sections.forEach(section => {
        if (section) {
            console.log('إخفاء القسم:', section.id);
            section.style.display = 'none';
        }
    });
}

// تعريف أسماء الأقسام
const sectionNames = {
    'homeSection': 'الرئيسية',
    'managersSettingsSection': 'إدارة المدراء',
    'departmentsSettingsSection': 'إدارة الأقسام',
    'requestsEntrySection': 'إدخال الطلبات',
    'reportsSection': 'التقارير',
    'vacationFormSection': 'نموذج طلب إجازة',
    'travelFormSection': 'نموذج طلب سفر'
};

// تحديث دالة عرض القسم
function showSection(sectionId) {
    console.log('عرض القسم:', sectionId);

    // إخفاء جميع الأقسام
    hideAllSections();

    // عرض القسم المطلوب
    const section = document.getElementById(sectionId);
    console.log('عنصر القسم:', section);

    if (section) {
        section.style.display = 'block';
        console.log('تم تعيين عرض القسم إلى block');

        // تحديث التنقل النشط
        updateActiveNav(sectionId);

        // استخدام القيمة من كائن sectionNames أو استخدام القيمة الافتراضية
        const breadcrumbText = sectionNames[sectionId] || sectionId;
        console.log('نص التنقل:', breadcrumbText);

        // تحديث عنوان القسم الحالي في شريط التنقل
        updateBreadcrumb(breadcrumbText);

        // تنفيذ إجراءات خاصة بكل قسم
        if (sectionId === 'homeSection') {
            // تحديث لوحات المعلومات في الصفحة الرئيسية
            updateDashboardStats();

            // تحديث جدول الطلبات
            updateRequestsTable();

            // تحديث جدول المدراء المسافرين حاليا
            updateTravelingManagersTable();

            // تحديث عدادات الطلبات
            updateRequestsCounters();
        } else if (sectionId === 'requestsEntrySection') {
            // تحديث جدول الطلبات في واجهة إدخال الطلبات
            updateRequestsEntryTable();
        } else if (sectionId === 'reportsSection') {
            // تعيين التاريخ الافتراضي وتحميل بيانات التقارير
            setupReportsSection();
        }
    } else {
        console.error('لم يتم العثور على القسم:', sectionId);
    }
}

function showHome() {
    showSection('homeSection');

    // تحديث لوحات المعلومات في الصفحة الرئيسية
    updateDashboardStats();
}

function showManagersSettings() {
    showSection('managersSettingsSection');
}

function showDepartmentsSettings() {
    showSection('departmentsSettingsSection');
}

function showRequestsEntry() {
    showSection('requestsEntrySection');
}

function showReports() {
    showSection('reportsSection');
}

// دالة لإعداد قسم التقارير
async function setupReportsSection() {
    console.log('بدء إعداد قسم التقارير');

    try {
        showAlert('جاري تحميل بيانات التقارير...', 'info');

        // تعيين التاريخ الافتراضي (الشهر الحالي)
        const today = new Date();
        const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

        const startDateInput = document.getElementById('reportStartDate');
        const endDateInput = document.getElementById('reportEndDate');

        if (startDateInput) {
            startDateInput.value = formatDateForInput(firstDayOfMonth);
            console.log('تم تعيين تاريخ البداية الافتراضي:', startDateInput.value);
        } else {
            console.warn('لم يتم العثور على عنصر تاريخ البداية');
        }

        if (endDateInput) {
            endDateInput.value = formatDateForInput(lastDayOfMonth);
            console.log('تم تعيين تاريخ النهاية الافتراضي:', endDateInput.value);
        } else {
            console.warn('لم يتم العثور على عنصر تاريخ النهاية');
        }

        // تعبئة قوائم الفلترة من قاعدة البيانات
        await populateReportFilters();

        // إضافة مستمعات الأحداث لأزرار التقارير
        setupReportsEventListeners();

        // تحميل بيانات التقارير من قاعدة البيانات
        await loadReportsData();

        // إضافة مستمعات الأحداث مباشرة للأزرار (كاحتياط إضافي)
        const filterBtn = document.getElementById('filterReportsBtn');
        if (filterBtn) {
            filterBtn.onclick = function(event) {
                event.preventDefault();
                console.log('تم النقر على زر الفلترة (من خلال onclick)');
                filterReports();
                return false;
            };
        }

        const resetFiltersBtn = document.getElementById('resetFiltersBtn');
        if (resetFiltersBtn) {
            resetFiltersBtn.onclick = function(event) {
                event.preventDefault();
                console.log('تم النقر على زر إعادة تعيين الفلترة (من خلال onclick)');
                resetReportFilters();
                return false;
            };
        }

        console.log('تم إكمال إعداد قسم التقارير بنجاح');
        showAlert('تم تحميل بيانات التقارير بنجاح', 'success');
    } catch (error) {
        console.error('حدث خطأ أثناء إعداد قسم التقارير:', error);
        showAlert('حدث خطأ أثناء إعداد قسم التقارير: ' + error.message, 'danger');
    }
}

// دالة لتعبئة قوائم الفلترة في صفحة التقارير
async function populateReportFilters() {
    try {
        console.log('تعبئة قوائم الفلترة في صفحة التقارير');

        // تعبئة قائمة المدراء
        const managerFilter = document.getElementById('reportManagerFilter');
        if (managerFilter) {
            // عرض مؤشر التحميل في القائمة المنسدلة
            managerFilter.innerHTML = '<option value="">جاري تحميل المدراء...</option>';
            managerFilter.disabled = true;

            try {
                // محاولة جلب بيانات المدراء من قاعدة البيانات
                const managers = await getManagers();
                console.log('تم جلب المدراء من قاعدة البيانات:', managers.length);

                // إضافة خيارات المدراء
                let managerOptions = '<option value="">كل المدراء</option>';

                if (managers && managers.length > 0) {
                    managers.forEach(manager => {
                        const managerName = manager.full_name || manager.fullName || manager.manager_name || 'مدير بدون اسم';
                        const managerId = manager.id || manager.manager_id;
                        managerOptions += `<option value="${managerId}">${managerName}</option>`;
                    });
                    console.log('تم إضافة', managers.length, 'مدير إلى قائمة الفلترة');
                } else {
                    console.warn('لم يتم العثور على مدراء في قاعدة البيانات');

                    // استخدام البيانات المحلية كاحتياط
                    const localManagers = JSON.parse(localStorage.getItem('managers') || '[]');
                    localManagers.forEach(manager => {
                        const managerName = manager.full_name || manager.fullName || 'مدير بدون اسم';
                        managerOptions += `<option value="${manager.id}">${managerName}</option>`;
                    });
                    console.log('تم استخدام', localManagers.length, 'مدير من التخزين المحلي');
                }

                managerFilter.innerHTML = managerOptions;
            } catch (error) {
                console.error('خطأ في جلب بيانات المدراء من قاعدة البيانات:', error);

                // استخدام البيانات المحلية في حالة الخطأ
                const localManagers = JSON.parse(localStorage.getItem('managers') || '[]');

                let managerOptions = '<option value="">كل المدراء</option>';
                localManagers.forEach(manager => {
                    const managerName = manager.full_name || manager.fullName || 'مدير بدون اسم';
                    managerOptions += `<option value="${manager.id}">${managerName}</option>`;
                });

                managerFilter.innerHTML = managerOptions;
                console.log('تم استخدام', localManagers.length, 'مدير من التخزين المحلي بعد فشل الاتصال بقاعدة البيانات');
            } finally {
                managerFilter.disabled = false;
            }
        }

        // تعبئة قائمة الأقسام
        const departmentFilter = document.getElementById('reportDepartmentFilter');
        if (departmentFilter) {
            // عرض مؤشر التحميل في القائمة المنسدلة
            departmentFilter.innerHTML = '<option value="">جاري تحميل الأقسام...</option>';
            departmentFilter.disabled = true;

            try {
                // محاولة استخراج الأقسام من بيانات المدراء في قاعدة البيانات
                const managers = await getManagers();

                // استخراج الأقسام الفريدة
                const departments = [...new Set(managers.map(manager =>
                    manager.department || manager.main_workplace || manager.mainWorkplace || manager.manager_department
                ))].filter(Boolean);

                // إضافة خيارات الأقسام
                let departmentOptions = '<option value="">كل الأقسام</option>';

                if (departments && departments.length > 0) {
                    departments.forEach(department => {
                        departmentOptions += `<option value="${department}">${department}</option>`;
                    });
                    console.log('تم إضافة', departments.length, 'قسم إلى قائمة الفلترة');
                } else {
                    console.warn('لم يتم العثور على أقسام في قاعدة البيانات');

                    // استخدام البيانات المحلية كاحتياط
                    const localManagers = JSON.parse(localStorage.getItem('managers') || '[]');
                    const localDepartments = [...new Set(localManagers.map(manager =>
                        manager.department || manager.main_workplace || manager.mainWorkplace
                    ))].filter(Boolean);

                    localDepartments.forEach(department => {
                        departmentOptions += `<option value="${department}">${department}</option>`;
                    });
                    console.log('تم استخدام', localDepartments.length, 'قسم من التخزين المحلي');
                }

                departmentFilter.innerHTML = departmentOptions;
            } catch (error) {
                console.error('خطأ في جلب بيانات الأقسام من قاعدة البيانات:', error);

                // استخدام البيانات المحلية في حالة الخطأ
                const localManagers = JSON.parse(localStorage.getItem('managers') || '[]');
                const localDepartments = [...new Set(localManagers.map(manager =>
                    manager.department || manager.main_workplace || manager.mainWorkplace
                ))].filter(Boolean);

                let departmentOptions = '<option value="">كل الأقسام</option>';
                localDepartments.forEach(department => {
                    departmentOptions += `<option value="${department}">${department}</option>`;
                });

                departmentFilter.innerHTML = departmentOptions;
                console.log('تم استخدام', localDepartments.length, 'قسم من التخزين المحلي بعد فشل الاتصال بقاعدة البيانات');
            } finally {
                departmentFilter.disabled = false;
            }
        }

        console.log('تم الانتهاء من تعبئة قوائم الفلترة بنجاح');

    } catch (error) {
        console.error('خطأ في تعبئة قوائم الفلترة:', error);
        showAlert('حدث خطأ في تعبئة قوائم الفلترة: ' + error.message, 'danger');
    }
}

// دالة لإعداد مستمعات الأحداث لأزرار التقارير
function setupReportsEventListeners() {
    try {
        console.log('إعداد مستمعات الأحداث لأزرار التقارير');

        // زر عرض النتائج
        const filterBtn = document.getElementById('filterReportsBtn');
        if (filterBtn) {
            // إزالة جميع مستمعات الأحداث السابقة
            const newFilterBtn = filterBtn.cloneNode(true);
            filterBtn.parentNode.replaceChild(newFilterBtn, filterBtn);

            // إضافة مستمع الحدث الجديد
            newFilterBtn.addEventListener('click', function(event) {
                event.preventDefault();
                console.log('تم النقر على زر الفلترة');
                filterReports();
            });

            console.log('تم إضافة مستمع الحدث لزر الفلترة');
        } else {
            console.warn('لم يتم العثور على زر الفلترة');
        }

        // زر إعادة تعيين الفلترة
        const resetFiltersBtn = document.getElementById('resetFiltersBtn');
        if (resetFiltersBtn) {
            // إزالة جميع مستمعات الأحداث السابقة
            const newResetBtn = resetFiltersBtn.cloneNode(true);
            resetFiltersBtn.parentNode.replaceChild(newResetBtn, resetFiltersBtn);

            // إضافة مستمع الحدث الجديد
            newResetBtn.addEventListener('click', function(event) {
                event.preventDefault();
                console.log('تم النقر على زر إعادة تعيين الفلترة');
                resetReportFilters();
            });

            console.log('تم إضافة مستمع الحدث لزر إعادة تعيين الفلترة');
        }

        // زر تصدير Excel
        const exportExcelBtn = document.getElementById('exportExcelBtn');
        if (exportExcelBtn) {
            // إزالة جميع مستمعات الأحداث السابقة
            const newExportExcelBtn = exportExcelBtn.cloneNode(true);
            exportExcelBtn.parentNode.replaceChild(newExportExcelBtn, exportExcelBtn);

            // إضافة مستمع الحدث الجديد
            newExportExcelBtn.addEventListener('click', function(event) {
                event.preventDefault();
                console.log('تم النقر على زر تصدير Excel');
                exportReportsToExcel();
            });
        }

        // زر تصدير PDF
        const exportPdfBtn = document.getElementById('exportPdfBtn');
        if (exportPdfBtn) {
            // إزالة جميع مستمعات الأحداث السابقة
            const newExportPdfBtn = exportPdfBtn.cloneNode(true);
            exportPdfBtn.parentNode.replaceChild(newExportPdfBtn, exportPdfBtn);

            // إضافة مستمع الحدث الجديد
            newExportPdfBtn.addEventListener('click', function(event) {
                event.preventDefault();
                console.log('تم النقر على زر تصدير PDF');
                exportReportsToPdf();
            });
        }

        // مستمعات الأحداث للفلترة المباشرة
        const searchInput = document.getElementById('reportSearchInput');
        if (searchInput) {
            // إزالة مستمعات الأحداث السابقة
            const newSearchInput = searchInput.cloneNode(true);
            searchInput.parentNode.replaceChild(newSearchInput, searchInput);

            // إضافة مستمع الحدث الجديد
            newSearchInput.addEventListener('input', function() {
                // تأخير البحث لتحسين الأداء
                clearTimeout(this.timer);
                this.timer = setTimeout(() => {
                    console.log('تم تغيير قيمة حقل البحث إلى:', this.value);
                    filterReports();
                }, 300);
            });

            console.log('تم إضافة مستمع الحدث لحقل البحث');
        }

        // إضافة مستمعات الأحداث لعناصر الفلترة الأخرى
        const filterElements = [
            'reportRequestType',
            'reportManagerFilter',
            'reportDepartmentFilter',
            'reportStartDate',
            'reportEndDate'
        ];

        filterElements.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element) {
                // إزالة مستمعات الأحداث السابقة
                const newElement = element.cloneNode(true);
                element.parentNode.replaceChild(newElement, element);

                // إضافة مستمع الحدث الجديد
                newElement.addEventListener('change', function() {
                    console.log(`تم تغيير قيمة ${elementId} إلى:`, this.value);
                    filterReports();
                });

                console.log(`تم إضافة مستمع الحدث لعنصر الفلترة: ${elementId}`);
            } else {
                console.warn(`لم يتم العثور على عنصر الفلترة: ${elementId}`);
            }
        });

        console.log('تم إكمال إعداد مستمعات الأحداث لأزرار التقارير بنجاح');

        // تطبيق الفلترة الأولية
        setTimeout(() => {
            filterReports();
        }, 500);

    } catch (error) {
        console.error('خطأ في إعداد مستمعات الأحداث لأزرار التقارير:', error);
        showAlert('حدث خطأ في إعداد مستمعات الأحداث للفلترة', 'danger');
    }
}

// دالة لتحميل بيانات التقارير
async function loadReportsData() {
    console.log('[Debug] loadReportsData: بدء تحميل البيانات.');
    try {
        console.log('تحميل بيانات التقارير');

        // عرض مؤشر التحميل
        const tableBody = document.querySelector('#reportsTable tbody');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-2">جاري تحميل البيانات من قاعدة البيانات...</p>
                    </td>
                </tr>
            `;
        } else {
            console.warn('لم يتم العثور على جسم جدول التقارير لعرض مؤشر التحميل');
        }

        // الحصول على بيانات الطلبات من قاعدة البيانات باستخدام API Client
        let travelRequests = [];
        let vacationRequests = [];

        try {
            // استخدام API Client لجلب البيانات من قاعدة البيانات
            travelRequests = await getTravelRequests();
            console.log('[Debug] loadReportsData: تم تحميل طلبات السفر من قاعدة البيانات:', travelRequests.length);
        } catch (e) {
            console.error('[Debug] loadReportsData: خطأ في جلب طلبات السفر من قاعدة البيانات:', e);
            showAlert('فشل في جلب طلبات السفر من قاعدة البيانات. استخدام البيانات المحلية.', 'warning');

            // استخدام البيانات المحلية كاحتياط
            try {
                travelRequests = JSON.parse(localStorage.getItem('travelRequests') || '[]');
                console.log('[Debug] loadReportsData: تم تحميل طلبات السفر من التخزين المحلي:', travelRequests.length);
            } catch (localError) {
                console.error('[Debug] loadReportsData: خطأ في تحليل طلبات السفر من التخزين المحلي:', localError);
                travelRequests = [];
            }
        }

        try {
            // استخدام API Client لجلب البيانات من قاعدة البيانات
            vacationRequests = await getVacationRequests();
            console.log('[Debug] loadReportsData: تم تحميل طلبات الإجازة من قاعدة البيانات:', vacationRequests.length);
        } catch (e) {
            console.error('[Debug] loadReportsData: خطأ في جلب طلبات الإجازة من قاعدة البيانات:', e);
            showAlert('فشل في جلب طلبات الإجازة من قاعدة البيانات. استخدام البيانات المحلية.', 'warning');

            // استخدام البيانات المحلية كاحتياط
            try {
                vacationRequests = JSON.parse(localStorage.getItem('vacationRequests') || '[]');
                console.log('[Debug] loadReportsData: تم تحميل طلبات الإجازة من التخزين المحلي:', vacationRequests.length);
            } catch (localError) {
                console.error('[Debug] loadReportsData: خطأ في تحليل طلبات الإجازة من التخزين المحلي:', localError);
                vacationRequests = [];
            }
        }

        // إذا لم تكن هناك بيانات، استخدم البيانات التجريبية
        if (travelRequests.length === 0 && vacationRequests.length === 0) {
            console.log('[Debug] loadReportsData: لا توجد بيانات من قاعدة البيانات أو التخزين المحلي. استخدام بيانات تجريبية.');
            createDummyDataIfNeeded();

            // إعادة محاولة تحميل البيانات من التخزين المحلي بعد إنشاء البيانات التجريبية
            try {
                travelRequests = JSON.parse(localStorage.getItem('travelRequests') || '[]');
                vacationRequests = JSON.parse(localStorage.getItem('vacationRequests') || '[]');
                console.log('[Debug] loadReportsData: تم تحميل البيانات التجريبية:', travelRequests.length, vacationRequests.length);
            } catch (e) {
                console.error('[Debug] loadReportsData: خطأ في تحميل البيانات التجريبية:', e);
            }
        }

        // تحويل طلبات السفر إلى التنسيق المطلوب
        const formattedTravelRequests = travelRequests.map(request => {
            if (!request || typeof request !== 'object') {
                console.warn('[Debug] loadReportsData: تم تخطي طلب سفر غير صالح:', request);
                return null;
            }

            // تحديد أسماء الحقول بناءً على مصدر البيانات (API أو localStorage)
            const departureDate = request.departure_date || request.departureDate;
            const returnDate = request.return_date || request.returnDate;
            const submissionDate = request.created_at || request.submission_date || request.submissionDate || new Date().toISOString();
            const managerName = request.manager_name || request.fullName;
            const managerId = request.manager_id || request.managerId;
            const position = request.manager_position || request.position;
            const department = request.manager_department || request.department;

            console.log(`[Debug] loadReportsData: معالجة طلب سفر رقم ${request.id}, تاريخ المغادرة: ${departureDate}, تاريخ العودة: ${returnDate}`);

            const formattedRequest = {
                id: request.id,
                type: 'travel',
                requestType: 'travel', // التأكد من أن هذا يتطابق مع قيم الفلتر
                managerName: managerName,
                managerId: managerId,
                position: position,
                department: department,
                createdAt: submissionDate,
                startDate: departureDate,
                endDate: returnDate,
                status: request.status || 'pending'
            };

            console.log(`[Debug] loadReportsData: تم تنسيق طلب سفر رقم ${formattedRequest.id}, تاريخ البداية: ${formattedRequest.startDate}, تاريخ النهاية: ${formattedRequest.endDate}`);
            return formattedRequest;
        }).filter(Boolean); // هنا يمكن إضافة المزيد من الدوال حسب الحاجة
// مثلاً، دوال خاصة بالتقارير أو إعدادات المستخدمين المتقدمة

        // تحويل طلبات الإجازة إلى التنسيق المطلوب
        const formattedVacationRequests = vacationRequests.map(request => {
            if (!request || typeof request !== 'object') {
                console.warn('[Debug] loadReportsData: تم تخطي طلب إجازة غير صالح:', request);
                return null;
            }

            // تحديد أسماء الحقول بناءً على مصدر البيانات (API أو localStorage)
            const startDate = request.start_date || request.startDate;
            const endDate = request.end_date || request.endDate;
            const submissionDate = request.created_at || request.submission_date || request.submissionDate || new Date().toISOString();
            const managerName = request.manager_name || request.fullName;
            const managerId = request.manager_id || request.managerId;
            const position = request.manager_position || request.position;
            const department = request.manager_department || request.department;

            console.log(`[Debug] loadReportsData: معالجة طلب إجازة رقم ${request.id}, تاريخ البداية: ${startDate}, تاريخ النهاية: ${endDate}`);

            const formattedRequest = {
                id: request.id,
                type: 'vacation',
                requestType: 'vacation', // التأكد من أن هذا يتطابق مع قيم الفلتر
                managerName: managerName,
                managerId: managerId,
                position: position,
                department: department,
                createdAt: submissionDate,
                startDate: startDate,
                endDate: endDate,
                status: request.status || 'pending'
            };

            console.log(`[Debug] loadReportsData: تم تنسيق طلب إجازة رقم ${formattedRequest.id}, تاريخ البداية: ${formattedRequest.startDate}, تاريخ النهاية: ${formattedRequest.endDate}`);
            return formattedRequest;
        }).filter(Boolean); // إزالة القيم الفارغة

        // دمج الطلبات
        const allRequests = [...formattedTravelRequests, ...formattedVacationRequests];
        console.log('[Debug] loadReportsData: جميع الطلبات (مدمجة ومنسقة):', allRequests.length);

        // تخزين البيانات في متغير عام للاستخدام في الفلترة
        window.reportsData = allRequests;
        console.log('[Debug] loadReportsData: تم تعبئة window.reportsData بـ', allRequests.length, 'طلب.');

        // عرض البيانات في الجدول
        displayReportsData(allRequests);

        // عرض رسالة نجاح
        if (allRequests.length > 0) {
            showAlert(`تم تحميل ${allRequests.length} طلب بنجاح`, 'success');
        } else {
            showAlert('لا توجد بيانات للعرض', 'info');
        }

    } catch (error) {
        console.error('[Debug] loadReportsData: Error during data loading:', error);
        console.error('خطأ في تحميل بيانات التقارير:', error);

        // عرض رسالة الخطأ
        const tableBody = document.querySelector('#reportsTable tbody');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        حدث خطأ أثناء تحميل البيانات: ${error.message}
                    </td>
                </tr>
            `;
        }

        showAlert('حدث خطأ أثناء تحميل البيانات: ' + error.message, 'danger');
    }
}

// دالة لإنشاء بيانات تجريبية إذا لم تكن موجودة
function createDummyDataIfNeeded() {
    try {
        console.log('تم تعطيل إنشاء البيانات التجريبية - استخدام قاعدة البيانات فقط');
    } catch (error) {
        console.error('خطأ في إنشاء البيانات التجريبية:', error);
    }
}

// دالة لعرض بيانات التقارير في الجدول
function displayReportsData(requests) {
    console.log('[Debug] displayReportsData: بدء عرض البيانات. عدد الطلبات للعرض:', requests ? requests.length : 'غير معرف');
    if (requests) {
        console.log('[Debug] displayReportsData: الطلبات المستلمة:', JSON.parse(JSON.stringify(requests)));
    }

    try {
        console.log('عرض بيانات التقارير في الجدول', requests ? requests.length : 0);

        const tableBody = document.querySelector('#reportsTable tbody');
        if (!tableBody) {
            console.error('[Debug] displayReportsData: لم يتم العثور على جسم جدول التقارير.');
            showAlert('حدث خطأ أثناء عرض البيانات: لم يتم العثور على جدول التقارير', 'danger');
            return;
        }

        if (!requests || requests.length === 0) {
            console.log('[Debug] displayReportsData: لا توجد طلبات للعرض أو مصفوفة الطلبات فارغة. عرض رسالة "لا توجد بيانات".');
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center">
                        <i class="fas fa-info-circle me-2"></i>
                        لا توجد بيانات متطابقة مع معايير البحث
                    </td>
                </tr>
            `;
            return;
        }

        // تنسيق التواريخ
        const formatDate = (dateString) => {
            if (!dateString) return '-';
            try {
                // Ensure dateString is valid before attempting to create a Date object
                const date = new Date(dateString);
                if (isNaN(date.getTime())) { // Check if date is invalid
                    console.warn(`[Debug] displayReportsData: Invalid date string encountered: ${dateString}`);
                    return dateString; // Return original string if invalid
                }
                return date.toLocaleDateString('ar-EG', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                });
            } catch (e) {
                console.warn(`[Debug] displayReportsData: Error formatting date string: ${dateString}`, e);
                return dateString; // Return original string on error
            }
        };

        // إنشاء صفوف الجدول
        const rows = requests.map(request => `
            <tr>
                <td>#${request.id}</td>
                <td>
                    <i class="fas ${request.type === 'vacation' ? 'fa-calendar' : 'fa-plane'} text-${request.type === 'vacation' ? 'success' : 'primary'} me-2"></i>
                    ${request.type === 'vacation' ? 'طلب إجازة' : 'طلب سفر'}
                </td>
                <td>${request.managerName || '-'}</td>
                <td>${request.position || '-'}</td>
                <td>${formatDate(request.createdAt)}</td>
                <td>${formatDate(request.startDate)}</td>
                <td>${formatDate(request.endDate)}</td>
                <td>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-info" title="عرض التفاصيل" onclick="showRequestDetails(${request.id}, '${request.type}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" title="تعديل" onclick="editRequest(${request.id}, '${request.type}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" title="حذف" onclick="deleteRequest(${request.id}, '${request.type}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        tableBody.innerHTML = rows;
        console.log('[Debug] displayReportsData: Table updated with rows.'); // Log added

    } catch (error) {
        console.error('[Debug] displayReportsData: Error displaying data:', error); // Log added
        console.error('خطأ في عرض بيانات التقارير:', error);

        // عرض رسالة الخطأ
        const tableBody = document.querySelector('#reportsTable tbody');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        حدث خطأ أثناء عرض البيانات: ${error.message}
                    </td>
                </tr>
            `;
        }
    }
}

// دالة لإعادة تعيين فلاتر التقارير
function resetReportFilters() {
    try {
        console.log('إعادة تعيين فلاتر التقارير');
        showAlert('جاري إعادة تعيين الفلاتر...', 'info');

        // تعيين التاريخ الافتراضي (الشهر الحالي)
        const today = new Date();
        const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

        // إعادة تعيين حقول التاريخ
        const startDateInput = document.getElementById('reportStartDate');
        const endDateInput = document.getElementById('reportEndDate');

        if (startDateInput) {
            startDateInput.value = formatDateForInput(firstDayOfMonth);
            console.log('تم إعادة تعيين تاريخ البداية إلى:', startDateInput.value);
        } else {
            console.warn('لم يتم العثور على عنصر تاريخ البداية');
        }

        if (endDateInput) {
            endDateInput.value = formatDateForInput(lastDayOfMonth);
            console.log('تم إعادة تعيين تاريخ النهاية إلى:', endDateInput.value);
        } else {
            console.warn('لم يتم العثور على عنصر تاريخ النهاية');
        }

        // إعادة تعيين القوائم المنسدلة
        const requestTypeSelect = document.getElementById('reportRequestType');
        const managerFilterSelect = document.getElementById('reportManagerFilter');
        const departmentFilterSelect = document.getElementById('reportDepartmentFilter');
        const searchInput = document.getElementById('reportSearchInput');

        if (requestTypeSelect) {
            requestTypeSelect.value = '';
            console.log('تم إعادة تعيين فلتر نوع الطلب');
        }

        if (managerFilterSelect) {
            managerFilterSelect.value = '';
            console.log('تم إعادة تعيين فلتر المدير');
        }

        if (departmentFilterSelect) {
            departmentFilterSelect.value = '';
            console.log('تم إعادة تعيين فلتر القسم');
        }

        if (searchInput) {
            searchInput.value = '';
            console.log('تم إعادة تعيين حقل البحث');
        }

        // إعادة تحميل البيانات
        loadReportsData();

        // تطبيق الفلترة بعد إعادة التعيين
        setTimeout(() => {
            try {
                filterReports();
                console.log('تم تطبيق الفلترة بعد إعادة التعيين');
                showAlert('تم إعادة تعيين الفلاتر بنجاح', 'success');
            } catch (filterError) {
                console.error('خطأ في تطبيق الفلترة بعد إعادة التعيين:', filterError);
                showAlert('تم إعادة تعيين الفلاتر ولكن حدث خطأ في تطبيق الفلترة', 'warning');
            }
        }, 500);

    } catch (error) {
        console.error('خطأ في إعادة تعيين فلاتر التقارير:', error);
        showAlert('حدث خطأ أثناء إعادة تعيين الفلاتر: ' + error.message, 'danger');
    }
}

// دالة لتنسيق التاريخ للإدخال في حقول التاريخ
function formatDateForInput(dateObj) {
    if (!dateObj) return '';
    try {
        const date = dateObj instanceof Date ? dateObj : new Date(dateObj);

        // التحقق من صحة التاريخ
        if (isNaN(date.getTime())) {
            console.error('تاريخ غير صالح:', dateObj);
            return '';
        }

        // تنسيق التاريخ بتنسيق YYYY-MM-DD المطلوب لحقول التاريخ
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0'); // الشهور تبدأ من 0
        const day = String(date.getDate()).padStart(2, '0');

        const formattedDate = `${year}-${month}-${day}`;
        console.log(`تنسيق التاريخ: ${dateObj} -> ${formattedDate}`);
        return formattedDate;
    } catch (e) {
        console.error('خطأ في تنسيق التاريخ:', e, dateObj);
        return '';
    }
}

// دالة مساعدة للتحقق من صحة التاريخ
function isValidDate(dateString) {
    if (!dateString) return false;

    try {
        const date = new Date(dateString);
        return !isNaN(date.getTime());
    } catch (e) {
        console.error('خطأ في التحقق من صحة التاريخ:', e, dateString);
        return false;
    }
}

// دالة للحصول على البيانات المفلترة الحالية
function getCurrentFilteredData() {
    const tableBody = document.querySelector('#reportsTable tbody');
    const rows = tableBody.querySelectorAll('tr');
    const data = [];

    if (rows.length === 0 || (rows.length === 1 && rows[0].querySelector('td').colSpan === 8)) {
        return data; // لا توجد بيانات أو صف رسالة فقط
    }

    // استخراج البيانات من صفوف الجدول المعروضة حاليًا
    // هذا يفترض أن البيانات المعروضة في الجدول هي البيانات المفلترة
    // إذا كان هناك مصدر آخر للبيانات المفلترة، يجب استخدامه هنا
    // في هذا السياق، سنفترض أن window.reportsData يحتوي على البيانات الأصلية
    // و filterReports تقوم بتحديث الجدول بناءً على الفلاتر
    // لذا، نحتاج إلى إعادة تطبيق الفلاتر للحصول على البيانات المفلترة الحالية

    // الحصول على قيم الفلترة
    const startDate = document.getElementById('reportStartDate').value;
    const endDate = document.getElementById('reportEndDate').value;
    const requestType = document.getElementById('reportRequestType').value;
    const managerId = document.getElementById('reportManagerFilter').value;
    const departmentFilterValueFromElement = document.getElementById('reportDepartmentFilter').value; // تمت إضافة قراءة فلتر القسم هنا خارج الحلقة
    const searchTerm = document.getElementById('reportSearchInput').value.toLowerCase();

    if (!window.reportsData) return [];

    return window.reportsData.filter(request => {
        // إضافة التحقق من أن الطلب كائن صالح وليس فارغًا
        if (!request || typeof request !== 'object') {
            console.warn('تم تخطي عنصر غير صالح في بيانات التقارير:', request);
            return false; // تجاهل العناصر غير الصالحة
        }
        try {
            // فلترة التاريخ المعدلة للتحقق من تداخل فترة الطلب مع فترة الفلتر (مع تحسين التحقق من صحة التاريخ)
            const reportStartFilterInput = startDate ? new Date(startDate) : null;
            const reportEndFilterInput = endDate ? new Date(endDate) : null;

            // التحقق من صحة تواريخ الفلتر نفسها
            const isValidReportStartFilter = reportStartFilterInput && !isNaN(reportStartFilterInput.getTime());
            const isValidReportEndFilter = reportEndFilterInput && !isNaN(reportEndFilterInput.getTime());

            if (isValidReportStartFilter) reportStartFilterInput.setHours(0, 0, 0, 0);
            if (isValidReportEndFilter) reportEndFilterInput.setHours(23, 59, 59, 999);

            const reqActualStartDate = request.startDate ? new Date(request.startDate) : null;
            const reqActualEndDate = request.endDate ? new Date(request.endDate) : null;

            const isValidReqStartDate = reqActualStartDate && !isNaN(reqActualStartDate.getTime());
            const isValidReqEndDate = reqActualEndDate && !isNaN(reqActualEndDate.getTime());

            if (isValidReqStartDate && isValidReqEndDate) {
                // إذا كانت تواريخ الطلب صالحة
                reqActualStartDate.setHours(0, 0, 0, 0);
                reqActualEndDate.setHours(23, 59, 59, 999);

                let dateMatch = true;
                // تطبيق الفلترة فقط إذا كانت تواريخ الفلتر صالحة
                if (isValidReportStartFilter && reqActualEndDate < reportStartFilterInput) {
                    dateMatch = false;
                }
                if (isValidReportEndFilter && reqActualStartDate > reportEndFilterInput) {
                    dateMatch = false;
                }
                if (!dateMatch) return false;

            } else if (isValidReportStartFilter || isValidReportEndFilter) {
                // إذا كانت هناك فلاتر تاريخ نشطة (وصالحة)، ولكن تواريخ الطلب غير صالحة أو مفقودة، فاستبعد الطلب
                return false;
            }
            // إذا لم تكن هناك فلاتر تاريخ نشطة، أو إذا كانت تواريخ الطلب غير صالحة ولكن لا توجد فلاتر تاريخ،
            // فإن الطلب يمر من هذا الجزء من الفلترة (قد يتم فلترته بمعايير أخرى).
            // فلترة نوع الطلب
            if (requestType && request.requestType !== requestType) return false;
            // فلترة المدير
            if (managerId && String(request.managerId) !== String(managerId)) return false; // توحيد المقارنة
            // فلترة القسم
            // const departmentFilterValue = document.getElementById('reportDepartmentFilter').value; // تم نقله للخارج
            if (departmentFilterValueFromElement && request.department !== departmentFilterValueFromElement) return false; // استخدام المتغير المقروء في الخارج

            // فلترة البحث
            if (searchTerm) {
                const searchFields = [
                    request.id ? String(request.id) : '', // تمت إضافة رقم الطلب لحقول البحث في getCurrentFilteredData
                    request.managerName,
                    request.position,
                    request.department
                ].filter(Boolean).map(field => String(field).toLowerCase());
                if (!searchFields.some(field => field.includes(searchTerm))) return false;
            }
            return true;
        } catch (error) {
            console.error('خطأ في فلترة الطلب (getCurrentFilteredData):', error, request);
            return false;
        }
    });
}

// دالة لفلترة التقارير
function filterReports() {
    console.log('[Debug] filterReports: بدء عملية الفلترة...');
    try {
        // التحقق من وجود البيانات
        if (!window.reportsData) {
            console.warn('[Debug] filterReports: window.reportsData غير معرف. لا توجد بيانات للفلترة.');
            displayReportsData([]);
            showAlert('لا توجد بيانات للفلترة', 'warning');
            return;
        }

        if (window.reportsData.length === 0) {
            console.warn('[Debug] filterReports: window.reportsData فارغ. لا توجد بيانات للفلترة.');
            displayReportsData([]);
            showAlert('لا توجد بيانات للفلترة', 'warning');
            return;
        }

        console.log('[Debug] filterReports: البيانات الأصلية:', window.reportsData.length, 'طلب');

        // الحصول على قيم الفلترة - استخدام طريقة أكثر أمانًا للحصول على القيم
        let startDateStr = '';
        let endDateStr = '';
        let requestTypeFilter = '';
        let managerIdFilter = '';
        let departmentFilter = '';
        let searchTermFilter = '';

        const startDateElement = document.getElementById('reportStartDate');
        if (startDateElement) startDateStr = startDateElement.value || '';

        const endDateElement = document.getElementById('reportEndDate');
        if (endDateElement) endDateStr = endDateElement.value || '';

        const requestTypeElement = document.getElementById('reportRequestType');
        if (requestTypeElement) requestTypeFilter = requestTypeElement.value || '';

        const managerFilterElement = document.getElementById('reportManagerFilter');
        if (managerFilterElement) managerIdFilter = managerFilterElement.value || '';

        const departmentFilterElement = document.getElementById('reportDepartmentFilter');
        if (departmentFilterElement) departmentFilter = departmentFilterElement.value || '';

        const searchInputElement = document.getElementById('reportSearchInput');
        if (searchInputElement) searchTermFilter = (searchInputElement.value || '').toLowerCase();

        console.log('[Debug] filterReports: معايير الفلترة:', {
            startDate: startDateStr,
            endDate: endDateStr,
            requestType: requestTypeFilter,
            managerId: managerIdFilter,
            department: departmentFilter,
            searchTerm: searchTermFilter
        });

        // تحقق مما إذا كانت هناك أي معايير فلترة نشطة
        const hasActiveFilters = startDateStr || endDateStr || requestTypeFilter ||
                                managerIdFilter || departmentFilter || searchTermFilter;

        if (!hasActiveFilters) {
            console.log('[Debug] filterReports: لا توجد معايير فلترة نشطة، عرض جميع البيانات');
            displayReportsData(window.reportsData);
            return;
        }

        // تطبيق الفلترة
        const filteredData = window.reportsData.filter(request => {
            if (!request || typeof request !== 'object') {
                return false;
            }

            // 1. فلترة التاريخ
            let dateMatch = true;
            if (startDateStr || endDateStr) {
                if (!request.startDate || !request.endDate) {
                    dateMatch = false;
                } else {
                    try {
                        const reqStartDate = new Date(request.startDate);
                        const reqEndDate = new Date(request.endDate);

                        if (isNaN(reqStartDate.getTime()) || isNaN(reqEndDate.getTime())) {
                            dateMatch = false;
                        } else {
                            reqStartDate.setHours(0, 0, 0, 0);
                            reqEndDate.setHours(23, 59, 59, 999);

                            if (startDateStr) {
                                const filterStartDate = new Date(startDateStr);
                                filterStartDate.setHours(0, 0, 0, 0);
                                if (reqEndDate < filterStartDate) {
                                    dateMatch = false;
                                }
                            }

                            if (dateMatch && endDateStr) {
                                const filterEndDate = new Date(endDateStr);
                                filterEndDate.setHours(23, 59, 59, 999);
                                if (reqStartDate > filterEndDate) {
                                    dateMatch = false;
                                }
                            }
                        }
                    } catch (e) {
                        console.warn(`[Debug] filterReports: خطأ في معالجة التواريخ للطلب ${request.id}:`, e);
                        dateMatch = false;
                    }
                }
            }

            // 2. فلترة نوع الطلب
            let typeMatch = true;
            if (requestTypeFilter && request.requestType !== requestTypeFilter) {
                typeMatch = false;
            }

            // 3. فلترة المدير
            let managerMatch = true;
            if (managerIdFilter) {
                if (!request.managerId) {
                    managerMatch = false;
                } else if (String(request.managerId) !== String(managerIdFilter)) {
                    managerMatch = false;
                }
            }

            // 4. فلترة القسم
            let departmentMatch = true;
            if (departmentFilter && request.department !== departmentFilter) {
                departmentMatch = false;
            }

            // 5. فلترة البحث النصي
            let searchMatch = true;
            if (searchTermFilter) {
                const searchFields = [
                    request.id ? String(request.id) : '',
                    request.managerName || '',
                    request.position || '',
                    request.department || ''
                ].filter(Boolean).map(field => String(field).toLowerCase());

                searchMatch = searchFields.some(field => field.includes(searchTermFilter));
            }

            // النتيجة النهائية - يجب أن تتطابق جميع معايير الفلترة
            return dateMatch && typeMatch && managerMatch && departmentMatch && searchMatch;
        });

        console.log('[Debug] filterReports: عدد النتائج بعد الفلترة:', filteredData.length);

        // عرض البيانات المفلترة
        displayReportsData(filteredData);

        // عرض رسالة إذا لم يتم العثور على نتائج
        if (filteredData.length === 0 && hasActiveFilters) {
            showAlert('لم يتم العثور على نتائج مطابقة لمعايير البحث.', 'info');
        } else if (filteredData.length > 0) {
            showAlert(`تم العثور على ${filteredData.length} نتيجة مطابقة لمعايير البحث.`, 'success');
        }

    } catch (error) {
        console.error('[Debug] filterReports: خطأ عام في وظيفة الفلترة:', error);
        showAlert('حدث خطأ أثناء فلترة البيانات: ' + error.message, 'danger');
        // الاحتياط: عرض البيانات الأصلية
        displayReportsData(window.reportsData || []);
    }
}

// دالة لتصدير التقارير إلى Excel - استخدام الدالة الجديدة من ملف export-excel.js
function exportReportsToExcel() {
    // استدعاء الدالة من ملف export-excel.js
    if (typeof window.exportReportsToExcel === 'function') {
        window.exportReportsToExcel();
    } else {
        console.error('دالة تصدير Excel غير متوفرة. تأكد من تضمين ملف export-excel.js');
        showAlert('حدث خطأ: دالة تصدير Excel غير متوفرة', 'danger');
    }
}

// دالة للحصول على البيانات المفلترة الحالية
function getCurrentFilteredData() {
    // إذا كانت هناك بيانات مفلترة حالية، استخدمها
    const tableBody = document.querySelector('#reportsTable tbody');
    if (tableBody) {
        const rows = tableBody.querySelectorAll('tr');
        if (rows.length > 0 && !rows[0].querySelector('td[colspan]')) {
            // هناك صفوف بيانات في الجدول
            const filteredData = [];
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length >= 7) {
                    const requestType = cells[1].textContent.includes('سفر') ? 'travel' : 'vacation';

                    // استخراج الحالة من النص داخل الشارة (إذا كان موجودًا)
                    let statusText = '';
                    let status = 'pending';

                    if (cells.length >= 8) {
                        const statusBadge = cells[7].querySelector('.badge');
                        if (statusBadge) {
                            statusText = statusBadge.textContent.trim();
                        } else {
                            statusText = cells[7].textContent.trim();
                        }

                        if (statusText.includes('قيد الانتظار')) {
                            status = 'pending';
                        } else if (statusText.includes('موافق عليه')) {
                            status = 'approved';
                        } else if (statusText.includes('مرفوض')) {
                            status = 'rejected';
                        } else if (statusText.includes('مكتمل')) {
                            status = 'completed';
                        }
                    }

                    // استخراج تواريخ البداية والنهاية
                    let startDate = cells[5].textContent.trim();
                    let endDate = cells[6].textContent.trim();

                    // التحقق من صحة التواريخ
                    if (startDate === '-' || !startDate) {
                        // استخدام تاريخ افتراضي إذا كان التاريخ غير متوفر
                        startDate = '2023-01-01';
                    }

                    if (endDate === '-' || !endDate) {
                        // استخدام تاريخ افتراضي إذا كان التاريخ غير متوفر
                        endDate = '2023-01-15';
                    }

                    filteredData.push({
                        id: cells[0].textContent.trim().replace('#', ''),
                        requestType: requestType,
                        type: requestType,
                        managerName: cells[2].textContent.trim(),
                        position: cells[3].textContent.trim(),
                        department: cells[4].textContent.trim(),
                        startDate: startDate,
                        endDate: endDate,
                        status: status
                    });
                }
            });
            console.log('تم استخراج البيانات المفلترة من الجدول:', filteredData.length);
            return filteredData;
        }
    }

    // إذا لم تكن هناك بيانات مفلترة، استخدم البيانات الأصلية
    console.log('استخدام البيانات الأصلية:', window.reportsData ? window.reportsData.length : 0);

    // التأكد من أن جميع العناصر في البيانات الأصلية تحتوي على تواريخ صالحة
    const validatedData = (window.reportsData || []).map(item => {
        // نسخة من العنصر لتجنب تعديل البيانات الأصلية
        const newItem = { ...item };

        // التحقق من تاريخ البداية
        if (!newItem.startDate || newItem.startDate === '-') {
            newItem.startDate = '2023-01-01';
        }

        // التحقق من تاريخ النهاية
        if (!newItem.endDate || newItem.endDate === '-') {
            newItem.endDate = '2023-01-15';
        }

        return newItem;
    });

    return validatedData;
}

// دالة لتصدير التقارير إلى PDF - استخدام الدالة الجديدة من ملف export-pdf.js
function exportReportsToPdf() {
    try {
        console.log('بدء تصدير التقارير إلى PDF من script.js');

        // استدعاء الدالة من ملف export-pdf.js
        if (typeof window.createPdfReport === 'function') {
            // استدعاء الدالة مباشرة من الملف الخارجي
            console.log('استدعاء دالة createPdfReport من ملف export-pdf.js');
            window.createPdfReport();
        } else {
            console.error('دالة تصدير PDF غير متوفرة. تأكد من تضمين ملف export-pdf.js');
            showAlert('حدث خطأ: دالة تصدير PDF غير متوفرة', 'danger');
        }
    } catch (error) {
        console.error('خطأ في تصدير التقارير إلى PDF:', error);
        showAlert('حدث خطأ أثناء تصدير التقارير إلى PDF: ' + error.message, 'danger');
    }
}

// دالة لعرض نموذج طلب السفر
function showTravelFormModal() {
    console.log('تم استدعاء دالة showTravelFormModal');
    
    try {
        // التأكد من أننا في قسم إدخال الطلبات أولاً
        showSection('travelFormSection');
        updateBreadcrumb('نموذج طلب سفر');
        
        // التأكد من أن قسم النموذج مرئي
        const travelFormSection = document.getElementById('travelFormSection');
        if (!travelFormSection) {
            console.error('لم يتم العثور على قسم نموذج طلب السفر');
            showAlert('خطأ في تحميل نموذج طلب السفر', 'danger');
            return;
        }
        
        // عرض قسم النموذج
        travelFormSection.style.display = 'block';
        
        // تعبئة قائمة الدول إذا كانت موجودة
        setTimeout(function() {
            try {
                populateCountriesList();
                console.log('تم تعبئة قائمة الدول');
                
                // تحديث قوائم المدراء
                updateManagersDropdowns();
                console.log('تم تحديث قوائم المدراء');
            } catch (e) {
                console.error('خطأ في تعبئة البيانات:', e);
            }
        }, 300);
        
        console.log('تم عرض نموذج طلب السفر بنجاح');
    } catch (error) {
        console.error('خطأ في عرض نموذج طلب السفر:', error);
        showAlert('حدث خطأ في عرض نموذج طلب السفر: ' + error.message, 'danger');
    }
}

// دالة لعرض نموذج طلب الإجازة
function showVacationFormModal() {
    console.log('تم استدعاء دالة showVacationFormModal');

    try {
        // التأكد من أننا في قسم نموذج الإجازة
        showSection('vacationFormSection');
        updateBreadcrumb('نموذج طلب إجازة');

        // التأكد من أن قسم النموذج مرئي
        const vacationFormSection = document.getElementById('vacationFormSection');
        if (!vacationFormSection) {
            console.error('لم يتم العثور على قسم نموذج طلب الإجازة');
            showAlert('خطأ في تحميل نموذج طلب الإجازة', 'danger');
            return;
        }

        // عرض قسم النموذج
        vacationFormSection.style.display = 'block';

        // تعبئة البيانات اللازمة
        setTimeout(function() {
            try {
                // تحديث قوائم المدراء
                updateManagersDropdowns();
                console.log('تم تحديث قوائم المدراء');
                
                // أي تعبئة إضافية للبيانات يمكن إضافتها هنا
                // مثال: populateLeaveTypes();
            } catch (e) {
                console.error('خطأ في تعبئة البيانات:', e);
            }
        }, 300);

        console.log('تم عرض نموذج طلب الإجازة بنجاح');
    } catch (error) {
        console.error('خطأ في عرض نموذج طلب الإجازة:', error);
        showAlert('حدث خطأ في عرض نموذج طلب الإجازة: ' + error.message, 'danger');
    }
}

// دالة لعرض نموذج طلب السفر بالطريقة القديمة (للتوافق)
function showTravelForm() {
    console.log('تم استدعاء دالة showTravelForm - تحويل إلى النافذة المنبثقة');
    showTravelFormModal();
}

// دالة لعرض نموذج طلب الإجازة بالطريقة القديمة (للتوافق)
function showVacationForm() {
    console.log('تم استدعاء دالة showVacationForm - تحويل إلى النافذة المنبثقة');
    showVacationFormModal();
}

// هذه الدالة تم استبدالها بدالة أخرى أدناه

// تحديث التنقل النشط
function updateActiveNav(sectionId) {
    console.log('تحديث التنقل النشط للقسم:', sectionId);

    // إزالة الفئة النشطة من جميع الروابط
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });

    // إضافة الفئة النشطة للرابط المحدد
    const activeLink = document.querySelector(`.nav-link[data-section="${sectionId}"]`);
    if (activeLink) {
        activeLink.classList.add('active');
        console.log('تم تحديث الرابط النشط:', activeLink);
    } else {
        console.warn('لم يتم العثور على رابط للقسم:', sectionId);

        // محاولة العثور على الرابط باستخدام معرف مختلف
        if (sectionId === 'vacationFormSection' || sectionId === 'travelFormSection') {
            // إذا كان القسم هو نموذج طلب إجازة أو سفر، فقم بتنشيط رابط الصفحة الرئيسية
            const homeLink = document.querySelector('.nav-link[data-section="homeSection"]');
            if (homeLink) {
                homeLink.classList.add('active');
                console.log('تم تنشيط رابط الصفحة الرئيسية بدلاً من ذلك');
            }
        }
    }
}

// Form Submissions
document.addEventListener('DOMContentLoaded', function() {
    // Travel Form Submission
    const travelForm = document.getElementById('travelForm');
    if (travelForm) {
        travelForm.addEventListener('submit', handleTravelSubmit);
    }

    // Vacation Form Submission
    const vacationForm = document.getElementById('vacationForm');
    if (vacationForm) {
        vacationForm.addEventListener('submit', handleVacationSubmit);
    }

    // Initialize the home page
    showHome();

    // تحديث جدول الطلبات
    updateRequestsTable();
});

// Handle Travel Form Submission
async function handleTravelSubmit(event) {
    if (event) {
        event.preventDefault();
    }

    // التحقق من صحة التواريخ
    if (!validateDates()) {
        return;
    }

    // الحصول على عنصر القائمة المنسدلة للمدير
    const managerSelect = document.querySelector('#travelForm [name="fullName"]');
    if (!managerSelect || !managerSelect.value) {
        alert('يرجى اختيار المدير');
        return;
    }

    // الحصول على النص المعروض للمدير المحدد
    const selectedOption = managerSelect.options[managerSelect.selectedIndex];
    const managerName = selectedOption.textContent;

    // جمع بيانات النموذج
    const formData = {
        // بيانات مقدم الطلب
        managerId: managerSelect.value,
        fullName: managerName,
        position: document.querySelector('#travelForm [name="position"]').value,
        department: document.querySelector('#travelForm [name="department"]').value,

        // تفاصيل السفر
        travelCountry: document.querySelector('#travelForm [name="travelCountry"]').value,
        notificationDate: document.querySelector('#travelForm [name="notificationDate"]').value,
        departureDate: document.querySelector('#travelForm [name="departureDate"]').value,
        returnDate: document.querySelector('#travelForm [name="returnDate"]').value,
        travelType: document.querySelector('#travelForm [name="travelType"]').value,
        travelPurpose: document.querySelector('#travelForm [name="travelPurpose"]').value,

        // بيانات القائم بالأعمال
        deputyName: document.querySelector('#travelForm [name="deputyName"]').value,
        deputyPosition: document.querySelector('#travelForm [name="deputyPosition"]').value,
        deputyPhone: document.querySelector('#travelForm [name="deputyPhone"]').value,

        // تاريخ تقديم الطلب
        submissionDate: new Date().toISOString(),
        status: 'pending'
    };

    // التحقق من تحميل النموذج المعبأ
    const uploadedForm = document.querySelector('#travelForm input[name="mainAttachment"]');
    if (!uploadedForm.files.length) {
        alert('يرجى إرفاق النموذج المعبأ');
        return;
    }

    // إرسال الطلب إلى الخادم
    try {
        const response = await fetch('http://localhost:3001/api/travel-requests', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });

        if (!response.ok) {
            throw new Error('فشل في إرسال طلب السفر إلى الخادم');
        }

        // عرض رسالة نجاح
        showAlert('تم تقديم طلب السفر بنجاح', 'success');
    } catch (error) {
        console.error('خطأ في إرسال طلب السفر:', error);
        showAlert('حدث خطأ في تقديم طلب السفر: ' + error.message, 'danger');
        return;
    }

    // إعادة تعيين النموذج والبقاء في نفس الصفحة
    setTimeout(() => {
        document.getElementById('travelForm').reset();
        // البقاء في صفحة الطلبات بدلاً من الانتقال إلى الصفحة الرئيسية
        showAlert('يمكنك الآن إضافة طلب سفر جديد أو العودة إلى قائمة الطلبات', 'info');
    }, 1500);
}

// Handle Vacation Form Submission
async function handleVacationSubmit(event) {
    if (event) {
        event.preventDefault();
    }

    console.log('تم استدعاء دالة handleVacationSubmit');

    // التحقق من صحة التواريخ
    if (!validateVacationDates()) {
        return;
    }

    // الحصول على عنصر القائمة المنسدلة للمدير
    const managerSelect = document.querySelector('#vacationForm [name="fullName"]');
    if (!managerSelect || !managerSelect.value) {
        alert('يرجى اختيار المدير');
        return;
    }

    // الحصول على النص المعروض للمدير المحدد
    const selectedOption = managerSelect.options[managerSelect.selectedIndex];
    const managerName = selectedOption.textContent;

    // جمع بيانات النموذج
    const formData = {
        // بيانات مقدم الطلب
        managerId: managerSelect.value,
        fullName: managerName,
        position: document.querySelector('#vacationForm [name="position"]').value,
        department: document.querySelector('#vacationForm [name="department"]').value,

        // تفاصيل الإجازة
        vacationType: document.querySelector('#vacationForm [name="vacationType"]').value,
        duration: document.querySelector('#vacationForm [name="duration"]').value,
        startDate: document.querySelector('#vacationForm [name="startDate"]').value,
        endDate: document.querySelector('#vacationForm [name="endDate"]').value,

        // بيانات القائم بالأعمال
        deputyName: document.querySelector('#vacationForm [name="deputyName"]').value,
        deputyPosition: document.querySelector('#vacationForm [name="deputyPosition"]').value,
        deputyPhone: document.querySelector('#vacationForm [name="deputyPhone"]').value,

        // تاريخ تقديم الطلب
        submissionDate: new Date().toISOString(),
        status: 'pending'
    };

    // إرسال الطلب إلى الخادم
    try {
        const response = await fetch('http://localhost:3001/api/vacation-requests', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });

        if (!response.ok) {
            throw new Error('فشل في إرسال طلب الإجازة إلى الخادم');
        }

        // عرض رسالة نجاح
        showAlert('تم تقديم طلب الإجازة بنجاح', 'success');
    } catch (error) {
        console.error('خطأ في إرسال طلب الإجازة:', error);
        showAlert('حدث خطأ في تقديم طلب الإجازة: ' + error.message, 'danger');
        return;
    }

    // إعادة تعيين النموذج والبقاء في نفس الصفحة
    setTimeout(() => {
        document.getElementById('vacationForm').reset();
        // البقاء في نفس الصفحة بدلاً من الانتقال إلى الصفحة الرئيسية
        showAlert('يمكنك الآن إضافة طلب إجازة جديد أو العودة إلى قائمة الطلبات', 'info');
    }, 1500);
}

// دالة للتحقق من صحة تواريخ الإجازة
function validateVacationDates() {
    const startDate = document.querySelector('#vacationForm [name="startDate"]').value;
    const endDate = document.querySelector('#vacationForm [name="endDate"]').value;

    if (!startDate || !endDate) {
        alert('يرجى تحديد تاريخ بداية ونهاية الإجازة');
        return false;
    }

    const start = new Date(startDate);
    const end = new Date(endDate);

    if (start >= end) {
        alert('تاريخ نهاية الإجازة يجب أن يكون بعد تاريخ البداية');
        return false;
    }

    return true;
}

// دالة طباعة استمارة فارغة
function printEmptyForm(formType) {
    console.log('طباعة استمارة فارغة من نوع:', formType);
    alert('جاري طباعة استمارة ' + (formType === 'vacation' ? 'طلب إجازة' : 'طلب سفر') + ' فارغة...');

    // إنشاء نافذة جديدة للطباعة
    const printWindow = window.open('', '_blank');

    // تحديد عنوان الاستمارة
    const formTitle = formType === 'vacation' ? 'نموذج طلب إجازة' : 'نموذج طلب سفر';

    // إنشاء محتوى الاستمارة
    let formContent = '';

    if (formType === 'vacation') {
        // محتوى استمارة طلب الإجازة
        formContent = `
            <div class="container mt-4 mb-5">
                <div class="header-container mb-4">
                    <div class="row">
                        <div class="col-md-4 text-start">
                            <img src="https://upload.wikimedia.org/wikipedia/commons/8/82/Emblem_of_Yemen.svg" alt="شعار الجمهورية اليمنية" style="height: 100px;">
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="header-line" style="border-bottom: 2px solid #000; margin: 0 auto; width: 80%;"></div>
                        </div>
                        <div class="col-md-4 text-end">
                            <h4 class="mb-1 fw-bold">الجمهورية اليمنية</h4>
                            <h5 class="mb-1">وزارة الإدارة المحلية</h5>
                            <h5 class="mb-1">محافظة حضرموت</h5>
                            <h5 class="mb-3">مكتب المحافظ</h5>
                        </div>
                    </div>
                    <div class="header-line mt-2" style="border-bottom: 2px solid #000; width: 100%;"></div>
                    <h3 class="text-center mt-4 mb-4">نموذج طلب إجازة</h3>
                </div>

                <!-- بيانات مقدم الطلب -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">بيانات مقدم الطلب</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">الاسم الكامل:</label>
                                <div class="border-bottom border-dark py-2">
                                    &nbsp;
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">المنصب الحالي:</label>
                                <div class="border-bottom border-dark py-2">
                                    &nbsp;
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الإجازة -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">تفاصيل الإجازة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">نوع الإجازة:</label>
                                <div class="d-flex flex-column">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox">
                                        <label class="form-check-label">سنوية</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox">
                                        <label class="form-check-label">مرضية</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox">
                                        <label class="form-check-label">اضطرارية</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox">
                                        <label class="form-check-label">استثنائية</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">مدة الإجازة (بالأيام):</label>
                                <div class="border-bottom border-dark py-2">
                                    &nbsp;
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">تاريخ بداية الإجازة:</label>
                                <div class="border-bottom border-dark py-2">
                                    &nbsp;
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">تاريخ نهاية الإجازة:</label>
                                <div class="border-bottom border-dark py-2">
                                    &nbsp;
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- بيانات القائم بالأعمال -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">بيانات القائم بالأعمال</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">اسم القائم بالأعمال:</label>
                                <div class="border-bottom border-dark py-2">
                                    &nbsp;
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">المنصب الحالي:</label>
                                <div class="border-bottom border-dark py-2">
                                    &nbsp;
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">رقم الهاتف:</label>
                                <div class="border-bottom border-dark py-2">
                                    &nbsp;
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- التوقيعات -->
                <div class="row mt-5">
                    <div class="col-md-4 text-center">
                        <h5 class="mb-4">توقيع مقدم الطلب</h5>
                        <div class="border-bottom border-dark py-4 mb-2">
                            &nbsp;
                        </div>
                        <p>الاسم: .................................................</p>
                        <p>التاريخ: ...............................................</p>
                    </div>
                    <div class="col-md-4 text-center">
                        <h5 class="mb-4">توقيع القائم بالأعمال</h5>
                        <div class="border-bottom border-dark py-4 mb-2">
                            &nbsp;
                        </div>
                        <p>الاسم: .................................................</p>
                        <p>التاريخ: ...............................................</p>
                    </div>
                    <div class="col-md-4 text-center">
                        <h5 class="mb-4">اعتماد المدير المباشر</h5>
                        <div class="border-bottom border-dark py-4 mb-2">
                            &nbsp;
                        </div>
                        <p>الاسم: .................................................</p>
                        <p>التاريخ: ...............................................</p>
                    </div>
                </div>

                <!-- ملاحظات -->
                <div class="card mt-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">ملاحظات</h5>
                    </div>
                    <div class="card-body">
                        <div class="border-bottom border-dark py-5">
                            &nbsp;
                        </div>
                    </div>
                </div>

                <!-- رقم الاستمارة والتاريخ -->
                <div class="mt-4 d-flex justify-content-between">
                    <p>رقم الاستمارة: .................</p>
                    <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
                </div>
            </div>
        `;
    } else {
        // محتوى استمارة طلب السفر
        formContent = `
            <div class="container mt-4 mb-5">
                <div class="header-container mb-4">
                    <div class="row">
                        <div class="col-md-4 text-start">
                            <img src="https://upload.wikimedia.org/wikipedia/commons/8/82/Emblem_of_Yemen.svg" alt="شعار الجمهورية اليمنية" style="height: 100px;">
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="header-line" style="border-bottom: 2px solid #000; margin: 0 auto; width: 80%;"></div>
                        </div>
                        <div class="col-md-4 text-end">
                            <h4 class="mb-1 fw-bold">الجمهورية اليمنية</h4>
                            <h5 class="mb-1">وزارة الإدارة المحلية</h5>
                            <h5 class="mb-1">محافظة حضرموت</h5>
                            <h5 class="mb-3">مكتب المحافظ</h5>
                        </div>
                    </div>
                    <div class="header-line mt-2" style="border-bottom: 2px solid #000; width: 100%;"></div>
                    <h3 class="text-center mt-4 mb-4">نموذج طلب سفر</h3>
                </div>

                <!-- بيانات مقدم الطلب -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">بيانات مقدم الطلب</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">الاسم الكامل:</label>
                                <div class="border-bottom border-dark py-2">
                                    &nbsp;
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">المنصب الحالي:</label>
                                <div class="border-bottom border-dark py-2">
                                    &nbsp;
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل السفر -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">تفاصيل السفر</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">دولة السفر:</label>
                                <div class="border-bottom border-dark py-2">
                                    &nbsp;
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">تاريخ الإشعار:</label>
                                <div class="border-bottom border-dark py-2">
                                    &nbsp;
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">تاريخ السفر:</label>
                                <div class="border-bottom border-dark py-2">
                                    &nbsp;
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">تاريخ العودة:</label>
                                <div class="border-bottom border-dark py-2">
                                    &nbsp;
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">نوع الرحلة:</label>
                                <div class="d-flex flex-column">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox">
                                        <label class="form-check-label">رحلة عمل</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox">
                                        <label class="form-check-label">رحلة علاج</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox">
                                        <label class="form-check-label">رحلة خاصة</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label fw-bold">الغرض من السفر:</label>
                                <div class="border-bottom border-dark py-4">
                                    &nbsp;
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- بيانات القائم بالأعمال -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">بيانات القائم بالأعمال</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">اسم القائم بالأعمال:</label>
                                <div class="border-bottom border-dark py-2">
                                    &nbsp;
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">المنصب الحالي:</label>
                                <div class="border-bottom border-dark py-2">
                                    &nbsp;
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">رقم الهاتف:</label>
                                <div class="border-bottom border-dark py-2">
                                    &nbsp;
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- التوقيعات -->
                <div class="row mt-5">
                    <div class="col-md-4 text-center">
                        <h5 class="mb-4">توقيع مقدم الطلب</h5>
                        <div class="border-bottom border-dark py-4 mb-2">
                            &nbsp;
                        </div>
                        <p>الاسم: .................................................</p>
                        <p>التاريخ: ...............................................</p>
                    </div>
                    <div class="col-md-4 text-center">
                        <h5 class="mb-4">توقيع القائم بالأعمال</h5>
                        <div class="border-bottom border-dark py-4 mb-2">
                            &nbsp;
                        </div>
                        <p>الاسم: .................................................</p>
                        <p>التاريخ: ...............................................</p>
                    </div>
                    <div class="col-md-4 text-center">
                        <h5 class="mb-4">اعتماد المدير المباشر</h5>
                        <div class="border-bottom border-dark py-4 mb-2">
                            &nbsp;
                        </div>
                        <p>الاسم: .................................................</p>
                        <p>التاريخ: ...............................................</p>
                    </div>
                </div>

                <!-- ملاحظات -->
                <div class="card mt-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">ملاحظات</h5>
                    </div>
                    <div class="card-body">
                        <div class="border-bottom border-dark py-5">
                            &nbsp;
                        </div>
                    </div>
                </div>

                <!-- رقم الاستمارة والتاريخ -->
                <div class="mt-4 d-flex justify-content-between">
                    <p>رقم الاستمارة: .................</p>
                    <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
                </div>
            </div>
        `;
    }

    // كتابة محتوى الاستمارة في نافذة الطباعة
    printWindow.document.write(`
        <html dir="rtl">
        <head>
            <title>${formTitle}</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
            <style>
                @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

                body {
                    font-family: 'Tajawal', 'Arial', sans-serif;
                    padding: 20px;
                    background-color: #f9f9f9;
                }

                .container {
                    background-color: white;
                    padding: 30px;
                    box-shadow: 0 0 15px rgba(0,0,0,0.1);
                    border-radius: 8px;
                }

                .header-container {
                    position: relative;
                }

                .header-line {
                    border-bottom: 2px solid #000;
                }

                .card {
                    border: 1px solid #ddd;
                    margin-bottom: 20px;
                    border-radius: 8px;
                    overflow: hidden;
                }

                .card-header {
                    background-color: #f0f0f0;
                    padding: 12px 15px;
                    font-weight: bold;
                    border-bottom: 1px solid #ddd;
                }

                .card-body {
                    padding: 20px;
                }

                .form-check {
                    margin-bottom: 8px;
                }

                .border-bottom {
                    border-bottom: 1px solid #777 !important;
                }

                .fw-bold {
                    font-weight: 700 !important;
                }

                h3 {
                    font-weight: 700;
                    color: #333;
                }

                h4, h5 {
                    font-weight: 500;
                    color: #333;
                }

                @media print {
                    .no-print {
                        display: none;
                    }

                    .card {
                        break-inside: avoid;
                        border: 1px solid #000;
                    }

                    .card-header {
                        background-color: #e9e9e9 !important;
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                    }

                    body {
                        padding: 0;
                        background-color: white;
                    }

                    .container {
                        box-shadow: none;
                        padding: 0;
                    }

                    .header-container img {
                        print-color-adjust: exact;
                        -webkit-print-color-adjust: exact;
                    }
                }
            </style>
        </head>
        <body>
            ${formContent}
            <div class="text-center mt-4 no-print">
                <button class="btn btn-primary" onclick="window.print()">
                    <i class="fas fa-print me-2"></i>طباعة الاستمارة
                </button>
            </div>
        </body>
        </html>
    `);

    // إغلاق مستند الطباعة
    printWindow.document.close();
}

// التحقق من صحة التواريخ
function validateDates() {
    const notificationDate = document.querySelector('[name="notificationDate"]');
    const departureDate = document.querySelector('[name="departureDate"]');
    const returnDate = document.querySelector('[name="returnDate"]');

    if (notificationDate && departureDate && returnDate) {
        // التأكد من أن تاريخ الإشعار قبل تاريخ السفر
        if (new Date(notificationDate.value) > new Date(departureDate.value)) {
            alert('يجب أن يكون تاريخ الإشعار قبل تاريخ السفر');
            return false;
        }

        // التأكد من أن تاريخ العودة بعد تاريخ السفر
        if (new Date(returnDate.value) < new Date(departureDate.value)) {
            alert('يجب أن يكون تاريخ العودة بعد تاريخ السفر');
            return false;
        }
    }
    return true;
}

// Add Manager Form
function showAddManagerForm() {
    document.getElementById('addManagerForm').style.display = 'block';

    // Replace API call with local data
    const entitySelect = document.getElementById('managerEntity');
    if (entitySelect) {
        entitySelect.innerHTML = '<option value="">اختر الجهة</option>';

        // Get entities from localStorage instead of API
        const entities = JSON.parse(localStorage.getItem('governmentEntities')) || [];

        entities.forEach(entity => {
            const option = document.createElement('option');
            option.value = entity.id;
            option.textContent = entity.name;
            entitySelect.appendChild(option);
        });
    }
}

// دالة إضافة مدير جديد
async function addManager(event) {
    event.preventDefault();

    try {
        const managerData = {
            full_name: document.getElementById('managerFullName').value,
            birth_place: document.getElementById('managerBirthPlace').value,
            birth_date: document.getElementById('managerBirthDate').value,
            current_address: document.getElementById('managerCurrentAddress').value,
            contact_numbers: document.getElementById('managerContactNumbers').value,
            email: document.getElementById('managerEmail').value,
            education_degree: document.getElementById('managerEducationDegree').value,
            specialization: document.getElementById('managerSpecialization').value,
            education_date: document.getElementById('managerEducationDate').value,
            certificates: document.getElementById('managerCertificates').value,
            current_title: document.getElementById('managerCurrentTitle').value,
            job_title: document.getElementById('managerJobTitle').value,
            job_level: document.getElementById('managerJobLevel').value,
            employment_date: document.getElementById('managerEmploymentDate').value,
            main_workplace: document.getElementById('managerMainWorkplace').value,
            appointment_type: document.getElementById('managerAppointmentType').value,
            appointment_type_detail: document.getElementById('managerAppointmentTypeDetail').value,
            appointment_decision_number: document.getElementById('managerAppointmentDecisionNumber').value,
            appointment_decision_date: document.getElementById('managerAppointmentDecisionDate').value,
            workplace_address: document.getElementById('managerWorkplaceAddress').value
        };

        // التحقق من البيانات الإلزامية فقط (الاسم الكامل والمسمى الحالي)
        if (!managerData.full_name) {
            throw new Error('الرجاء إدخال الاسم الكامل');
        }
        if (!managerData.current_title) {
            throw new Error('الرجاء إدخال المسمى الحالي');
        }

        console.log('بيانات المدير المراد إضافته:', managerData);

        try {
            console.log('إضافة المدير إلى قاعدة البيانات...');

            // إرسال البيانات مباشرة إلى الخادم
            const response = await fetch('http://localhost:3001/api/managers', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(managerData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`فشل في إضافة المدير: ${errorData.message || response.statusText}`);
            }

            const result = await response.json();
            console.log('تم إضافة المدير بنجاح إلى قاعدة البيانات:', result);

            // تحديث جدول المدراء
            await updateManagersTable();
        } catch (error) {
            console.error('خطأ في إضافة المدير:', error);
            throw new Error('فشل في إضافة المدير: ' + error.message);
        }

        // إغلاق النافذة المنبثقة
        const modalElement = document.getElementById('addManagerModal');
        if (modalElement) {
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }
        }

        // إعادة تعيين النموذج
        const form = document.getElementById('addManagerForm');
        if (form) {
            form.reset();
        }

        // تحديث الجدول
        updateManagersTable();

        showAlert('تم إضافة المدير بنجاح', 'success');
    } catch (err) {
        console.error('خطأ في إضافة المدير:', err);
        showAlert(err.message, 'danger');
    }
}

// دالة تحديث جدول المدراء مع الفلترة
async function updateManagersTable(searchTerm = '', department = '', education = '') {
    try {
        console.log('تحديث جدول المدراء من قاعدة البيانات');

        // عرض مؤشر التحميل في الجدول
        const tableBody = document.querySelector('#managersTable tbody');
        if (!tableBody) {
            console.error('لم يتم العثور على جدول المدراء');
            return;
        }

        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل بيانات المدراء من قاعدة البيانات...</p>
                </td>
            </tr>
        `;

        // جلب بيانات المدراء من الخادم باستخدام API Client
        const managers = await apiClient.getManagers();
        console.log('تم العثور على', managers.length, 'مدير في النظام');

        // تحديث عداد المدراء في الصفحة الرئيسية
        const managersCountElement = document.getElementById('managersCount');
        if (managersCountElement) {
            managersCountElement.textContent = managers.length;
        }

        // لا نستخدم التخزين المحلي بعد الآن، نعتمد فقط على قاعدة البيانات

        tableBody.innerHTML = '';

        if (managers.length > 0) {
            // تطبيق الفلترة
            let filteredData = managers.filter(manager => {
                const matchesSearch = !searchTerm ||
                    (manager.full_name && manager.full_name.includes(searchTerm)) ||
                    (manager.email && manager.email.includes(searchTerm)) ||
                    (manager.contact_numbers && manager.contact_numbers.includes(searchTerm));

                const matchesDepartment = !department || manager.main_workplace === department;
                const matchesEducation = !education || manager.education_degree === education;

                return matchesSearch && matchesDepartment && matchesEducation;
            });

            console.log('عدد المدراء بعد الفلترة:', filteredData.length);

            // تحديث الجدول
            filteredData.forEach(manager => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${manager.full_name || ''}</td>
                    <td>${manager.current_title || ''}</td>
                    <td>${manager.main_workplace || ''}</td>
                    <td>${manager.education_degree || ''}</td>
                    <td>${manager.contact_numbers || ''}</td>
                    <td>${manager.email || ''}</td>
                    <td>${manager.employment_date ? new Date(manager.employment_date).toLocaleDateString('ar-SA') : ''}</td>
                    <td>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-info" onclick="showManagerDetailsModal(${manager.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="editManager(${manager.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteManager(${manager.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;
                tableBody.appendChild(row);
            });

            // تحديث عدد النتائج
            if (typeof updateResultsCount === 'function') {
                updateResultsCount(filteredData.length);
            }

            console.log('تم تحديث جدول المدراء بنجاح');
        } else {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center">لا يوجد مدراء مضافين</td>
                </tr>
            `;
            console.log('لا يوجد مدراء مضافين');
        }

        // تحديث قوائم المدراء في نماذج الطلبات
        updateManagersDropdowns();
    } catch (err) {
        console.error('خطأ في تحديث جدول المدراء من قاعدة البيانات:', err);

        // في حالة فشل الاتصال بالخادم، نعرض رسالة خطأ
        try {
            console.log('فشل في الاتصال بقاعدة البيانات');

            const tableBody = document.querySelector('#managersTable tbody');
            if (!tableBody) return;

            // عرض رسالة خطأ في الجدول
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center">
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            فشل في الاتصال بقاعدة البيانات. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.
                        </div>
                        <button class="btn btn-primary mt-2" onclick="updateManagersTable()">
                            <i class="fas fa-sync-alt me-2"></i>
                            إعادة المحاولة
                        </button>
                    </td>
                </tr>
            `;

            // تحديث عدد النتائج
            if (typeof updateResultsCount === 'function') {
                updateResultsCount(0);
            }
        } catch (fallbackError) {
            console.error('خطأ في استخدام البيانات المحلية للمدراء:', fallbackError);
            showAlert('حدث خطأ في تحديث جدول المدراء', 'danger');
        }
    }
}

// دالة تحديث قوائم المدراء في نماذج الطلبات
async function updateManagersDropdowns() {
    try {
        console.log('تحديث قوائم المدراء في نماذج الطلبات من قاعدة البيانات');

        // جلب بيانات المدراء من الخادم باستخدام API Client
        const managers = await apiClient.getManagers();

        // تحديث قائمة المدراء في نموذج طلب السفر
        const travelManagerSelect = document.querySelector('#travelForm select[name="fullName"]');
        if (travelManagerSelect) {
            // الاحتفاظ بالخيار الأول
            const firstOption = travelManagerSelect.options[0];
            travelManagerSelect.innerHTML = '';
            travelManagerSelect.appendChild(firstOption);

            // إضافة المدراء
            managers.forEach(manager => {
                const option = document.createElement('option');
                option.value = manager.id;
                option.textContent = manager.full_name || '';
                option.dataset.position = manager.current_title || '';
                option.dataset.department = manager.main_workplace || '';
                travelManagerSelect.appendChild(option);
            });
        }

        // تحديث قائمة المدراء في نموذج طلب الإجازة
        const vacationManagerSelect = document.querySelector('#vacationForm select[name="fullName"]');
        if (vacationManagerSelect) {
            // الاحتفاظ بالخيار الأول
            const firstOption = vacationManagerSelect.options[0];
            vacationManagerSelect.innerHTML = '';
            vacationManagerSelect.appendChild(firstOption);

            // إضافة المدراء
            managers.forEach(manager => {
                const option = document.createElement('option');
                option.value = manager.id;
                option.textContent = manager.full_name || '';
                option.dataset.position = manager.current_title || '';
                option.dataset.department = manager.main_workplace || '';
                vacationManagerSelect.appendChild(option);
            });
        }

        console.log('تم تحديث قوائم المدراء في نماذج الطلبات بنجاح');
    } catch (err) {
        console.error('خطأ في تحديث قوائم المدراء من قاعدة البيانات:', err);

        // في حالة فشل الاتصال بالخادم، نعرض رسالة خطأ
        try {
            console.log('فشل في الاتصال بقاعدة البيانات لتحديث قوائم المدراء');

            // تحديث قائمة المدراء في نموذج طلب السفر
            const travelManagerSelect = document.querySelector('#travelForm select[name="fullName"]');
            if (travelManagerSelect) {
                // الاحتفاظ بالخيار الأول
                const firstOption = travelManagerSelect.options[0];
                travelManagerSelect.innerHTML = '';
                travelManagerSelect.appendChild(firstOption);

                // إضافة خيار يشير إلى وجود خطأ
                const errorOption = document.createElement('option');
                errorOption.disabled = true;
                errorOption.textContent = 'فشل في تحميل المدراء من قاعدة البيانات';
                travelManagerSelect.appendChild(errorOption);
            }

            // تحديث قائمة المدراء في نموذج طلب الإجازة
            const vacationManagerSelect = document.querySelector('#vacationForm select[name="fullName"]');
            if (vacationManagerSelect) {
                // الاحتفاظ بالخيار الأول
                const firstOption = vacationManagerSelect.options[0];
                vacationManagerSelect.innerHTML = '';
                vacationManagerSelect.appendChild(firstOption);

                // إضافة خيار يشير إلى وجود خطأ
                const errorOption = document.createElement('option');
                errorOption.disabled = true;
                errorOption.textContent = 'فشل في تحميل المدراء من قاعدة البيانات';
                vacationManagerSelect.appendChild(errorOption);
            }

            console.log('تم تحديث قوائم المدراء مع رسالة خطأ');
        } catch (fallbackError) {
            console.error('خطأ في استخدام البيانات المحلية لتحديث قوائم المدراء:', fallbackError);
        }
    }
}

// دالة عرض تفاصيل المدير في نافذة منبثقة
async function showManagerDetailsModal(managerId) {
    try {
        console.log('جلب تفاصيل المدير من قاعدة البيانات');

        // عرض مؤشر التحميل
        let detailsContent = document.getElementById('managerDetailsContent');
        if (detailsContent) {
            detailsContent.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل بيانات المدير من قاعدة البيانات...</p>
                </div>
            `;
        }

        // عرض النافذة المنبثقة
        let modalInstance = new bootstrap.Modal(document.getElementById('managerDetailsModal'));
        modalInstance.show();

        // جلب بيانات المدراء من الخادم باستخدام API Client
        const managers = await apiClient.getManagers();

        // البحث عن المدير بواسطة المعرف
        const manager = managers.find(m => m.id == managerId);

        if (!manager) {
            showAlert('لم يتم العثور على بيانات المدير', 'danger');
            return;
        }

        // تحديث المحتوى بعد جلب البيانات
        detailsContent = document.getElementById('managerDetailsContent');

        // تنسيق التاريخ
        const formatDate = (dateString) => {
            if (!dateString) return '';
            try {
                return new Date(dateString).toLocaleDateString('ar-SA');
            } catch (e) {
                return dateString;
            }
        };

        // إنشاء محتوى تفاصيل المدير
        detailsContent.innerHTML = `
            <div class="row">
                <div class="col-12 mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="text-primary">${manager.full_name || ''}</h4>
                        <span class="badge bg-primary">${manager.current_title || ''}</span>
                    </div>
                </div>

                <!-- البيانات الشخصية -->
                <div class="col-12 mb-4">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0"><i class="fas fa-user me-2"></i>البيانات الشخصية</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="fw-bold">الاسم الكامل:</label>
                                    <p>${manager.full_name || ''}</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="fw-bold">مكان الميلاد:</label>
                                    <p>${manager.birth_place || ''}</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="fw-bold">تاريخ الميلاد:</label>
                                    <p>${formatDate(manager.birth_date)}</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="fw-bold">العنوان الحالي:</label>
                                    <p>${manager.current_address || ''}</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="fw-bold">أرقام الاتصال:</label>
                                    <p>${manager.contact_numbers || ''}</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="fw-bold">البريد الإلكتروني:</label>
                                    <p>${manager.email || ''}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المؤهلات العلمية -->
                <div class="col-12 mb-4">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>المؤهلات العلمية</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="fw-bold">الدرجة العلمية:</label>
                                    <p>${manager.education_degree || ''}</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="fw-bold">التخصص:</label>
                                    <p>${manager.specialization || ''}</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="fw-bold">تاريخ الحصول على المؤهل:</label>
                                    <p>${formatDate(manager.education_date)}</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="fw-bold">الشهادات الإضافية:</label>
                                    <p>${manager.certificates || ''}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- بيانات الوظيفة -->
                <div class="col-12 mb-4">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>بيانات الوظيفة</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="fw-bold">المسمى الحالي:</label>
                                    <p>${manager.current_title || ''}</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="fw-bold">المسمى الوظيفي:</label>
                                    <p>${manager.job_title || ''}</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="fw-bold">المرتبة الوظيفية:</label>
                                    <p>${manager.job_level || ''}</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="fw-bold">تاريخ التعيين:</label>
                                    <p>${formatDate(manager.employment_date)}</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="fw-bold">جهة العمل الرئيسية:</label>
                                    <p>${manager.main_workplace || ''}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- بيانات قرار التعيين -->
                <div class="col-12 mb-4">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0"><i class="fas fa-file-contract me-2"></i>بيانات قرار التعيين</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="fw-bold">نوع التعيين:</label>
                                    <p>${manager.appointment_type || ''}</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="fw-bold">تفاصيل نوع التعيين:</label>
                                    <p>${manager.appointment_type_detail || ''}</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="fw-bold">رقم قرار التعيين:</label>
                                    <p>${manager.appointment_decision_number || ''}</p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="fw-bold">تاريخ قرار التعيين:</label>
                                    <p>${formatDate(manager.appointment_decision_date)}</p>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label class="fw-bold">عنوان مقر العمل:</label>
                                    <p>${manager.workplace_address || ''}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // عرض النافذة المنبثقة
        const modal = new bootstrap.Modal(document.getElementById('managerDetailsModal'));
        modal.show();

    } catch (err) {
        console.error('خطأ في عرض تفاصيل المدير:', err);
        showAlert('حدث خطأ في عرض تفاصيل المدير', 'danger');
    }
}

// دالة طباعة تفاصيل المدير
function printManagerDetails() {
    const detailsContent = document.getElementById('managerDetailsContent');
    if (!detailsContent) return;

    const printWindow = window.open('', '_blank');

    printWindow.document.write(`
        <html dir="rtl">
        <head>
            <title>تفاصيل المدير</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
            <style>
                @media print {
                    .no-print { display: none; }
                    .card { border: 1px solid #ddd; margin-bottom: 20px; break-inside: avoid; }
                    .card-header { background-color: #f8f9fa !important; -webkit-print-color-adjust: exact; }
                }
                body { font-family: 'Arial', sans-serif; padding: 20px; }
            </style>
        </head>
        <body>
            <div class="container mt-4">
                <div class="text-center mb-4">
                    <h2>بيانات المدير</h2>
                    <p class="text-muted">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
                </div>

                ${detailsContent.innerHTML}

                <div class="text-center mt-4 no-print">
                    <button class="btn btn-primary" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                </div>
            </div>
            <script>
                window.onload = function() {
                    setTimeout(function() {
                        window.print();
                    }, 500);
                };
            </script>
        </body>
        </html>
    `);

    printWindow.document.close();
}

// دالة طباعة بيانات المدراء
function printManagersData() {
    const printWindow = window.open('', '_blank');
    const table = document.getElementById('managersTable');

    printWindow.document.write(`
        <html dir="rtl">
        <head>
            <title>بيانات المدراء</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                body { font-family: Arial, sans-serif; }
                @media print {
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <div class="container mt-4">
                <h2 class="text-center mb-4">بيانات المدراء</h2>
                ${table.outerHTML}
            </div>
            <script>window.print();</script>
        </body>
        </html>
    `);
}

// إضافة مستمعي الأحداث للفلترة
document.addEventListener('DOMContentLoaded', () => {
    const searchInput = document.getElementById('searchInput');
    const departmentFilter = document.getElementById('departmentFilter');
    const educationFilter = document.getElementById('educationFilter');

    // تحديث الجدول عند تغيير البحث
    searchInput.addEventListener('input', () => {
        updateManagersTable(
            searchInput.value,
            departmentFilter.value,
            educationFilter.value
        );
    });

    // تحديث الجدول عند تغيير الفلتر
    departmentFilter.addEventListener('change', () => {
        updateManagersTable(
            searchInput.value,
            departmentFilter.value,
            educationFilter.value
        );
    });

    educationFilter.addEventListener('change', () => {
        updateManagersTable(
            searchInput.value,
            departmentFilter.value,
            educationFilter.value
        );
    });

    // تحديث الجدول عند تحميل الصفحة
    updateManagersTable();
});

// دالة عرض نافذة تعديل المدير
async function editManager(managerId) {
    try {
        console.log('جلب بيانات المدير للتعديل من قاعدة البيانات، معرف المدير:', managerId);

        // عرض النافذة المنبثقة أولاً
        const modalElement = document.getElementById('editManagerModal');
        const modalInstance = new bootstrap.Modal(modalElement);
        modalInstance.show();

        // عرض مؤشر التحميل
        const editForm = document.getElementById('editManagerForm');
        if (editForm) {
            const formContent = editForm.innerHTML;
            editForm.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل بيانات المدير من قاعدة البيانات...</p>
                </div>
            `;

            // جلب بيانات المدراء من الخادم
            try {
                const response = await fetch('http://localhost:3001/api/managers');

                if (!response.ok) {
                    throw new Error(`فشل في جلب بيانات المدراء: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();

                // التحقق من تنسيق البيانات
                let managers = [];
                if (data.status === 'success' && Array.isArray(data.data)) {
                    managers = data.data;
                } else if (Array.isArray(data)) {
                    managers = data;
                } else {
                    throw new Error('تنسيق البيانات المستلمة غير متوقع');
                }

                // البحث عن المدير بواسطة المعرف
                const manager = managers.find(m => m.id == managerId);

                if (!manager) {
                    throw new Error(`لم يتم العثور على المدير بالمعرف ${managerId}`);
                }

                console.log('تم العثور على بيانات المدير:', manager);

                // استعادة نموذج التعديل
                editForm.innerHTML = formContent;

                // ملء النموذج ببيانات المدير
                document.getElementById('editManagerId').value = manager.id;
                document.getElementById('editManagerFullName').value = manager.full_name || '';
                document.getElementById('editManagerBirthPlace').value = manager.birth_place || '';
                document.getElementById('editManagerBirthDate').value = manager.birth_date ? manager.birth_date.split('T')[0] : '';
                document.getElementById('editManagerCurrentAddress').value = manager.current_address || '';
                document.getElementById('editManagerContactNumbers').value = manager.contact_numbers || '';
                document.getElementById('editManagerEmail').value = manager.email || '';

                document.getElementById('editManagerEducationDegree').value = manager.education_degree || '';
                document.getElementById('editManagerSpecialization').value = manager.specialization || '';
                document.getElementById('editManagerEducationDate').value = manager.education_date ? manager.education_date.split('T')[0] : '';
                document.getElementById('editManagerCertificates').value = manager.certificates || '';

                document.getElementById('editManagerCurrentTitle').value = manager.current_title || '';
                document.getElementById('editManagerJobTitle').value = manager.job_title || '';
                document.getElementById('editManagerJobLevel').value = manager.job_level || '';
                document.getElementById('editManagerEmploymentDate').value = manager.employment_date ? manager.employment_date.split('T')[0] : '';
                document.getElementById('editManagerMainWorkplace').value = manager.main_workplace || '';

                document.getElementById('editManagerAppointmentType').value = manager.appointment_type || '';
                document.getElementById('editManagerAppointmentTypeDetail').value = manager.appointment_type_detail || '';
                document.getElementById('editManagerAppointmentDecisionNumber').value = manager.appointment_decision_number || '';
                document.getElementById('editManagerAppointmentDecisionDate').value = manager.appointment_decision_date ? manager.appointment_decision_date.split('T')[0] : '';
                document.getElementById('editManagerWorkplaceAddress').value = manager.workplace_address || '';

            } catch (error) {
                console.error('خطأ في جلب بيانات المدير:', error);
                showAlert('حدث خطأ في جلب بيانات المدير: ' + error.message, 'danger');

                // استعادة نموذج التعديل
                editForm.innerHTML = formContent;

                // إغلاق النافذة المنبثقة
                modalInstance.hide();
            }
        }
    } catch (err) {
        console.error('خطأ في تحميل بيانات المدير للتعديل:', err);
        showAlert('حدث خطأ في تحميل بيانات المدير للتعديل', 'danger');
    }
}

// دالة تحديث بيانات المدير
async function updateManager() {
    try {
        // الحصول على بيانات النموذج
        const form = document.getElementById('editManagerForm');

        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const managerId = document.getElementById('editManagerId').value;
        console.log('تحديث بيانات المدير بالمعرف:', managerId);

        // إنشاء كائن بالبيانات المحدثة
        const updatedManager = {
            id: managerId,
            full_name: document.getElementById('editManagerFullName').value,
            birth_place: document.getElementById('editManagerBirthPlace').value,
            birth_date: document.getElementById('editManagerBirthDate').value,
            current_address: document.getElementById('editManagerCurrentAddress').value,
            contact_numbers: document.getElementById('editManagerContactNumbers').value,
            email: document.getElementById('editManagerEmail').value,

            education_degree: document.getElementById('editManagerEducationDegree').value,
            specialization: document.getElementById('editManagerSpecialization').value,
            education_date: document.getElementById('editManagerEducationDate').value,
            certificates: document.getElementById('editManagerCertificates').value,

            current_title: document.getElementById('editManagerCurrentTitle').value,
            job_title: document.getElementById('editManagerJobTitle').value,
            job_level: document.getElementById('editManagerJobLevel').value,
            employment_date: document.getElementById('editManagerEmploymentDate').value,
            main_workplace: document.getElementById('editManagerMainWorkplace').value,

            appointment_type: document.getElementById('editManagerAppointmentType').value,
            appointment_type_detail: document.getElementById('editManagerAppointmentTypeDetail').value,
            appointment_decision_number: document.getElementById('editManagerAppointmentDecisionNumber').value,
            appointment_decision_date: document.getElementById('editManagerAppointmentDecisionDate').value,
            workplace_address: document.getElementById('editManagerWorkplaceAddress').value
        };

        // عرض مؤشر التحميل
        const updateButton = document.querySelector('#editManagerModal .btn-primary');
        if (updateButton) {
            updateButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري التحديث...';
            updateButton.disabled = true;
        }

        // استخدام نقطة النهاية الرئيسية للمدراء مع طريقة POST
        const response = await fetch(`http://localhost:3001/api/managers`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                ...updatedManager,
                _method: 'PUT' // إضافة معلمة لتوضيح أن هذا تحديث وليس إضافة
            })
        });

        // التحقق من الاستجابة
        if (!response.ok) {
            let errorMessage = `فشل في تحديث بيانات المدير (${response.status}: ${response.statusText})`;
            console.error('استجابة الخادم غير ناجحة:', response.status, response.statusText);
            throw new Error(errorMessage);
        }

        // تحديث جدول المدراء
        await updateManagersTable();

        // إغلاق النافذة المنبثقة
        const modal = bootstrap.Modal.getInstance(document.getElementById('editManagerModal'));
        modal.hide();

        // عرض رسالة نجاح
        showAlert('تم تحديث بيانات المدير بنجاح', 'success');

    } catch (err) {
        console.error('خطأ في تحديث بيانات المدير:', err);
        showAlert(err.message || 'حدث خطأ في تحديث بيانات المدير', 'danger');
    } finally {
        // إعادة زر التحديث إلى حالته الطبيعية
        const updateButton = document.querySelector('#editManagerModal .btn-primary');
        if (updateButton) {
            updateButton.innerHTML = 'حفظ التغييرات';
            updateButton.disabled = false;
        }
    }
}

// دالة عرض نافذة تأكيد حذف المدير
async function deleteManager(managerId) {
    try {
        console.log('جلب بيانات المدير للحذف من قاعدة البيانات');

        // جلب بيانات المدراء من الخادم
        const response = await fetch('http://localhost:3001/api/managers');

        if (!response.ok) {
            throw new Error(`فشل في جلب بيانات المدراء: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        // التحقق من تنسيق البيانات
        let managers = [];
        if (data.status === 'success' && Array.isArray(data.data)) {
            managers = data.data;
        } else if (Array.isArray(data)) {
            managers = data;
        } else {
            throw new Error('تنسيق البيانات المستلمة غير متوقع');
        }

        // البحث عن المدير بواسطة المعرف
        const manager = managers.find(m => m.id == managerId);

        if (!manager) {
            throw new Error(`لم يتم العثور على المدير بالمعرف ${managerId}`);
        }

        console.log('تم العثور على بيانات المدير للحذف:', manager);

        // تعيين معرف واسم المدير في نافذة التأكيد
        document.getElementById('deleteManagerId').value = managerId;
        document.getElementById('deleteManagerName').textContent = manager.full_name || 'المحدد';

        // عرض النافذة المنبثقة
        const modal = new bootstrap.Modal(document.getElementById('deleteManagerModal'));
        modal.show();

    } catch (err) {
        console.error('خطأ في عرض نافذة تأكيد الحذف:', err);
        showAlert('حدث خطأ في عرض نافذة تأكيد الحذف: ' + err.message, 'danger');
    }
}

// دالة تأكيد حذف المدير
async function confirmDeleteManager() {
    try {
        const managerId = document.getElementById('deleteManagerId').value;
        console.log('حذف المدير بالمعرف:', managerId);

        // عرض مؤشر التحميل
        const deleteButton = document.querySelector('#deleteManagerModal .btn-danger');
        if (deleteButton) {
            deleteButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري الحذف...';
            deleteButton.disabled = true;
        }

        // استخدام نقطة النهاية الرئيسية للمدراء مع طريقة POST
        // نستخدم نفس نقطة النهاية التي تستخدم لإضافة المدراء، ولكن نضيف معرف المدير للإشارة إلى أنه حذف
        const response = await fetch(`http://localhost:3001/api/managers`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'delete',  // إضافة معلمة لتوضيح أن هذا حذف وليس إضافة
                id: managerId
            })
        });

        // التحقق من الاستجابة
        if (!response.ok) {
            let errorMessage = `فشل في حذف المدير (${response.status}: ${response.statusText})`;
            console.error('استجابة الخادم غير ناجحة:', response.status, response.statusText);
            throw new Error(errorMessage);
        }

        // تحديث جدول المدراء
        await updateManagersTable();

        // إغلاق النافذة المنبثقة
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteManagerModal'));
        modal.hide();

        // عرض رسالة نجاح
        showAlert('تم حذف المدير بنجاح', 'success');

    } catch (err) {
        console.error('خطأ في حذف المدير:', err);
        showAlert(err.message || 'حدث خطأ في حذف المدير', 'danger');
    } finally {
        // إعادة زر الحذف إلى حالته الطبيعية
        const deleteButton = document.querySelector('#deleteManagerModal .btn-danger');
        if (deleteButton) {
            deleteButton.innerHTML = 'تأكيد الحذف';
            deleteButton.disabled = false;
        }
    }
}

// Add Department Form
function showAddDepartmentForm() {
    const form = `
        <div class="modal fade" id="addDepartmentModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إضافة جهة جديدة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="addDepartmentForm">
                            <div class="mb-3">
                                <label class="form-label">اسم الجهة</label>
                                <input type="text" class="form-control" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea class="form-control" rows="3" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">عدد الموظفين</label>
                                <input type="number" class="form-control" required>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="saveDepartment()">حفظ</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', form);
    const modal = new bootstrap.Modal(document.getElementById('addDepartmentModal'));
    modal.show();
}

// Save Functions (These would typically interact with your backend)
function saveManager() {
    alert('تم حفظ بيانات المدير بنجاح!');
    const modal = bootstrap.Modal.getInstance(document.getElementById('addManagerModal'));
    modal.hide();
}

function saveDepartment() {
    alert('تم حفظ بيانات الجهة بنجاح!');
    const modal = bootstrap.Modal.getInstance(document.getElementById('addDepartmentModal'));
    modal.hide();
}

// هذه الدالة تم استبدالها بدالة أخرى في بداية الملف

// Handle Vacation Form Submission
async function handleVacationSubmit(event) {
    console.log('تم استدعاء دالة handleVacationSubmit');

    if (event) {
        event.preventDefault();
        console.log('تم منع السلوك الافتراضي للنموذج');
    }

    // الحصول على عنصر القائمة المنسدلة للمدير
    const managerSelect = document.querySelector('#vacationForm [name="fullName"]');
    console.log('عنصر القائمة المنسدلة للمدير:', managerSelect);

    if (!managerSelect || !managerSelect.value) {
        alert('يرجى اختيار المدير');
        console.error('لم يتم اختيار المدير');
        return;
    }

    // الحصول على النص المعروض للمدير المحدد
    const selectedOption = managerSelect.options[managerSelect.selectedIndex];
    const managerName = selectedOption.textContent;
    console.log('المدير المحدد:', managerName);

    // جمع بيانات النموذج
    console.log('جمع بيانات النموذج');

    const formData = {
        // بيانات مقدم الطلب
        managerId: managerSelect.value,
        fullName: managerName,
        position: document.querySelector('#vacationForm [name="position"]').value,
        department: document.querySelector('#vacationForm [name="department"]').value,

        // تفاصيل الإجازة
        vacationType: document.querySelector('#vacationForm [name="vacationType"]').value,
        duration: document.querySelector('#vacationForm [name="duration"]').value,
        startDate: document.querySelector('#vacationForm [name="startDate"]').value,
        endDate: document.querySelector('#vacationForm [name="endDate"]').value,
        reason: document.querySelector('#vacationForm [name="reason"]')?.value || '',

        // بيانات القائم بالأعمال
        deputyName: document.querySelector('#vacationForm [name="deputyName"]').value,
        deputyPosition: document.querySelector('#vacationForm [name="deputyPosition"]').value,
        deputyPhone: document.querySelector('#vacationForm [name="deputyPhone"]').value,
        deputyEmail: document.querySelector('#vacationForm [name="deputyEmail"]')?.value || '',

        // تاريخ تقديم الطلب
        submissionDate: new Date().toISOString(),
        status: 'pending'
    };

    console.log('بيانات النموذج:', formData);

    // إرسال الطلب إلى الخادم
    try {
        const response = await fetch('http://localhost:3001/api/vacation-requests', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });

        if (!response.ok) {
            throw new Error('فشل في إرسال طلب الإجازة إلى الخادم');
        }

        // عرض رسالة نجاح
        showAlert('تم تقديم طلب الإجازة بنجاح', 'success');
    } catch (error) {
        console.error('خطأ في إرسال طلب الإجازة:', error);
        showAlert('حدث خطأ في تقديم طلب الإجازة: ' + error.message, 'danger');
        return;
    }
    console.log('تم عرض رسالة النجاح');

    // إعادة توجيه المستخدم إلى الصفحة الرئيسية
    console.log('جاري إعادة توجيه المستخدم إلى الصفحة الرئيسية...');
    setTimeout(() => {
        document.getElementById('vacationForm').reset();
        console.log('تم إعادة تعيين النموذج');
    }, 1500);
}

// Calculate End Date
function calculateEndDate() {
    const startDate = document.querySelector('[name="startDate"]').value;
    const duration = document.querySelector('[name="duration"]').value;

    if (startDate && duration) {
        const endDateInput = document.querySelector('[name="endDate"]');
        const date = new Date(startDate);
        date.setDate(date.getDate() + parseInt(duration) - 1);
        endDateInput.value = date.toISOString().split('T')[0];
    }
}

// دالة للحصول على بيانات المدراء
async function getManagersData() {
    try {
        // الحصول على البيانات من API
        const response = await fetch('/api/managers');

        if (!response.ok) {
            throw new Error(`فشل في جلب بيانات المدراء: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.data && Array.isArray(data.data)) {
            return data.data;
        } else if (Array.isArray(data)) {
            return data;
        } else {
            console.warn('تنسيق البيانات المستلمة غير متوقع:', data);
            return [];
        }
    } catch (error) {
        console.error('خطأ في الحصول على بيانات المدراء:', error);
        return [];
    }
}

// دالة لتحميل المدراء من قاعدة البيانات
async function loadManagersFromDB() {
    try {
        console.log('جاري تحميل المدراء من قاعدة البيانات...');

        const response = await fetch('/api/managers');

        if (!response.ok) {
            throw new Error(`فشل في جلب بيانات المدراء: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('تم استلام بيانات من الخادم:', data);

        // التحقق من وجود البيانات بغض النظر عن اسم الخاصية
        if (data.data && Array.isArray(data.data) && data.data.length > 0) {
            console.log('تم تحميل المدراء بنجاح:', data.data.length, 'مدير');
            return data.data;
        } else if (data.status === 'success' && data.data && Array.isArray(data.data)) {
            console.log('تم تحميل المدراء بنجاح:', data.data.length, 'مدير');
            return data.data;
        } else {
            console.warn('لم يتم العثور على بيانات المدراء في الاستجابة:', data);
            return [];
        }
    } catch (error) {
        console.error('خطأ في تحميل المدراء من قاعدة البيانات:', error);
        return [];
    }
}

// دالة لتحديث قائمة المدراء في النماذج
async function updateManagersDropdown(selectElement) {
    if (!selectElement) {
        console.error('لم يتم تمرير عنصر القائمة المنسدلة');
        return;
    }

    console.log('تحديث قائمة المدراء في العنصر:', selectElement);

    // تفريغ القائمة
    selectElement.innerHTML = '<option value="">اختر المدير...</option>';

    // إضافة خيار "جاري التحميل..."
    const loadingOption = document.createElement('option');
    loadingOption.value = "";
    loadingOption.textContent = "جاري تحميل المدراء...";
    loadingOption.disabled = true;
    selectElement.appendChild(loadingOption);

    // تحميل المدراء من قاعدة البيانات
    const managers = await loadManagersFromDB();

    // تفريغ القائمة مرة أخرى
    selectElement.innerHTML = '<option value="">اختر المدير...</option>';

    if (!managers || managers.length === 0) {
        console.warn('لا توجد بيانات مدراء في النظام. يرجى إضافة مدراء من خلال نافذة إدارة المدراء أولاً.');

        // إضافة خيار توضيحي
        const option = document.createElement('option');
        option.value = "";
        option.textContent = "-- لا يوجد مدراء مضافين --";
        option.disabled = true;
        selectElement.appendChild(option);

        // لا نستخدم التخزين المحلي بعد الآن
        console.log('فشل في تحميل المدراء من قاعدة البيانات');

        return;
    }

    console.log('تم العثور على', managers.length, 'مدير في النظام');

    // إضافة المدراء إلى القائمة المنسدلة
    managers.forEach(manager => {
        const option = document.createElement('option');
        option.value = manager.id || manager.full_name;
        option.textContent = manager.full_name;
        option.dataset.position = manager.current_title || manager.job_title;
        option.dataset.department = manager.main_workplace;
        option.dataset.phone = manager.contact_numbers;
        option.dataset.email = manager.email;
        selectElement.appendChild(option);
    });

    console.log('تم إضافة المدراء إلى القائمة المنسدلة');

    // إضافة خاصية البحث للقائمة المنسدلة
    addSearchToSelect(selectElement);

    // إضافة مستمع حدث لتعبئة بيانات المدير المحدد
    selectElement.addEventListener('change', function() {
        console.log('تم تغيير المدير المحدد');

        const selectedOption = this.options[this.selectedIndex];
        if (!selectedOption.value) return;

        console.log('المدير المحدد:', selectedOption.textContent);
        console.log('البيانات المتاحة:', selectedOption.dataset);

        // تعبئة المنصب الحالي
        const formId = selectElement.closest('form').id;
        const formType = formId.replace('Form', '');

        const positionInput = document.querySelector(`#${formId} [name="position"]`);
        if (positionInput && selectedOption.dataset.position) {
            positionInput.value = selectedOption.dataset.position;
            console.log('تم تعبئة المنصب الحالي:', positionInput.value);
        }

        // إذا كان هناك حقول إضافية يمكن تعبئتها
        const departmentInput = document.querySelector(`#${formId} [name="department"]`);
        if (departmentInput && selectedOption.dataset.department) {
            departmentInput.value = selectedOption.dataset.department;
            console.log('تم تعبئة القسم/الإدارة:', departmentInput.value);
        }
    });
}

// دالة لإضافة خاصية البحث للقائمة المنسدلة
function addSearchToSelect(selectElement) {
    if (!selectElement) {
        console.error('لم يتم تمرير عنصر القائمة المنسدلة');
        return;
    }

    console.log('إضافة خاصية البحث للقائمة المنسدلة:', selectElement);

    // تعديل القائمة المنسدلة لتكون قابلة للبحث
    selectElement.setAttribute('data-live-search', 'true');
    selectElement.setAttribute('data-placeholder', 'اختر المدير أو اكتب للبحث...');
    selectElement.setAttribute('autocomplete', 'off');

    // إضافة مستمع حدث للبحث عند الكتابة في القائمة المنسدلة نفسها
    selectElement.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        console.log('البحث عن:', searchTerm);

        // الحصول على جميع الخيارات
        const options = this.querySelectorAll('option');
        console.log('عدد الخيارات:', options.length);

        let foundMatch = false;

        // فلترة الخيارات بناءً على مصطلح البحث
        options.forEach(option => {
            if (option.value === '') return; // تخطي الخيار الافتراضي

            const text = option.textContent.toLowerCase();
            const isVisible = text.includes(searchTerm);

            // إظهار أو إخفاء الخيارات
            option.style.display = isVisible ? '' : 'none';

            // اختيار أول خيار مطابق
            if (isVisible && !foundMatch) {
                foundMatch = true;
                option.selected = true;

                // تعبئة بيانات المدير المحدد تلقائياً
                const event = new Event('change');
                selectElement.dispatchEvent(event);

                console.log('تم اختيار أول خيار مطابق:', option.textContent);
            }
        });
    });

    // تعديل مظهر القائمة المنسدلة
    selectElement.style.width = '100%';
    selectElement.parentElement.style.position = 'relative';

    // إضافة أيقونة البحث
    const searchIcon = document.createElement('span');
    searchIcon.innerHTML = '<i class="fas fa-search"></i>';
    searchIcon.style.position = 'absolute';
    searchIcon.style.left = '10px';
    searchIcon.style.top = '50%';
    searchIcon.style.transform = 'translateY(-50%)';
    searchIcon.style.color = '#6c757d';

    // إضافة الأيقونة إلى الحاوية
    if (selectElement.parentElement.tagName === 'DIV') {
        selectElement.parentElement.appendChild(searchIcon);
    }

    console.log('تم إضافة خاصية البحث للقائمة المنسدلة بنجاح');
}

// دالة لتعبئة بيانات المدير المحدد تلقائياً
function fillManagerData(selectElement, formType) {
    if (!selectElement) return;

    console.log('إضافة مستمع حدث لتعبئة بيانات المدير في نموذج:', formType);

    // إزالة مستمعات الأحداث السابقة لتجنب التكرار
    selectElement.removeEventListener('change', handleManagerChange);

    // إضافة مستمع حدث جديد
    selectElement.addEventListener('change', handleManagerChange);

    function handleManagerChange() {
        console.log('تم تغيير المدير المحدد');

        const selectedOption = this.options[this.selectedIndex];
        if (!selectedOption.value) return;

        console.log('المدير المحدد:', selectedOption.textContent);
        console.log('البيانات المتاحة:', selectedOption.dataset);

        // تعبئة المنصب الحالي
        const positionInput = document.querySelector(`#${formType}Form [name="position"]`);
        if (positionInput && selectedOption.dataset.position) {
            positionInput.value = selectedOption.dataset.position;
            console.log('تم تعبئة المنصب الحالي:', positionInput.value);
        }

        // إذا كان هناك حقول إضافية يمكن تعبئتها
        const departmentInput = document.querySelector(`#${formType}Form [name="department"]`);
        if (departmentInput && selectedOption.dataset.department) {
            departmentInput.value = selectedOption.dataset.department;
            console.log('تم تعبئة القسم/الإدارة:', departmentInput.value);
        }

        const phoneInput = document.querySelector(`#${formType}Form [name="phone"]`);
        if (phoneInput && selectedOption.dataset.phone) {
            phoneInput.value = selectedOption.dataset.phone;
            console.log('تم تعبئة رقم الهاتف:', phoneInput.value);
        }

        const emailInput = document.querySelector(`#${formType}Form [name="email"]`);
        if (emailInput && selectedOption.dataset.email) {
            emailInput.value = selectedOption.dataset.email;
            console.log('تم تعبئة البريد الإلكتروني:', emailInput.value);
        }
    }
}

// إضافة مستمعات الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // استدعاء دالة عرض المدراء الغائبين إذا كانت الجداول موجودة في الصفحة الحالية
    if (document.getElementById('travelingManagersTable') && document.getElementById('vacationingManagersTable')) {
        displayAbsentManagers();
    }

    console.log('تحميل الصفحة وإعداد مستمعات الأحداث');

    // إعداد مستمعات الأحداث للنماذج
    setupFormEventListeners();

    // تحديث قائمة الطلبات في الصفحة الرئيسية
    updateRequestsList();
});

// تحديث قائمة الطلبات في الصفحة الرئيسية
async function updateRequestsList() {
    const tableBody = document.querySelector('#requestsTable tbody');
    if (!tableBody) return;

    try {
        // عرض مؤشر التحميل
        tableBody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل الطلبات من قاعدة البيانات...</p>
                </td>
            </tr>
        `;

        // جلب طلبات الإجازة من قاعدة البيانات
        const vacationResponse = await fetch('/api/vacation-requests');
        if (!vacationResponse.ok) {
            throw new Error(`فشل في جلب طلبات الإجازة: ${vacationResponse.status} ${vacationResponse.statusText}`);
        }
        const vacationData = await vacationResponse.json();
        const vacationRequests = vacationData.data || [];

        // جلب طلبات السفر من قاعدة البيانات
        const travelResponse = await fetch('/api/travel-requests');
        if (!travelResponse.ok) {
            throw new Error(`فشل في جلب طلبات السفر: ${travelResponse.status} ${travelResponse.statusText}`);
        }
        const travelData = await travelResponse.json();
        const travelRequests = travelData.data || [];

        // دمج جميع الطلبات وترتيبها حسب تاريخ التقديم
        const allRequests = [...vacationRequests, ...travelRequests]
            .sort((a, b) => new Date(b.submissionDate) - new Date(a.submissionDate));

        if (allRequests.length > 0) {
            tableBody.innerHTML = allRequests.map(request => `
                <tr>
                    <td>#${request.id}</td>
                    <td>
                        <i class="fas ${request.vacationType ? 'fa-calendar' : 'fa-plane'} text-${request.vacationType ? 'success' : 'primary'} me-2"></i>
                        ${request.vacationType ? 'طلب إجازة' : 'طلب سفر'}
                    </td>
                    <td>${new Date(request.submissionDate).toLocaleDateString('ar-EG', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit'
                    })}</td>
                    <td><span class="badge bg-warning">قيد المراجعة</span></td>
                    <td>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-info" title="عرض التفاصيل" onclick="showRequestDetails(${request.id}, '${request.vacationType ? 'vacation' : 'travel'}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-primary" title="تعديل" onclick="editRequest(${request.id}, '${request.vacationType ? 'vacation' : 'travel'}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" title="حذف" onclick="deleteRequest(${request.id}, '${request.vacationType ? 'vacation' : 'travel'}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        } else {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center">لا توجد طلبات مضافة</td>
                </tr>
            `;
        }
    } catch (error) {
        console.error('خطأ في تحديث قائمة الطلبات:', error);
        tableBody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center">
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        فشل في جلب الطلبات من قاعدة البيانات. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.
                    </div>
                    <button class="btn btn-primary mt-2" onclick="updateRequestsList()">
                        <i class="fas fa-sync-alt me-2"></i>
                        إعادة المحاولة
                    </button>
                </td>
            </tr>
        `;
    }
}

// أنماط الطباعة المشتركة
const printStyles = `
    body {
        font-family: 'Traditional Arabic', Arial, sans-serif;
        padding: 20px;
        direction: rtl;
    }
    .header {
        text-align: center;
        margin-bottom: 30px;
        border-bottom: 2px solid #000;
        padding-bottom: 20px;
    }
    .org-logo {
        text-align: center;
        margin-bottom: 20px;
        font-size: 24px;
        // عرض رسالة نجاح
        showAlert('تم تقديم طلب السفر بنجاح', 'success');

        // العودة إلى واجهة إدخال الطلبات
        showRequestsEntry();

        // تحديث جدول الطلبات
        updateRequestsEntryTable();
    } catch (error) {
        console.error('خطأ في تقديم طلب السفر:', error);
        showAlert('حدث خطأ في تقديم طلب السفر: ' + error.message, 'danger');
    } finally {
        // إعادة زر التقديم إلى حالته الأصلية
        const submitButton = document.querySelector('#travelForm button[type="submit"]');
        if (submitButton) {
            submitButton.innerHTML = '<i class="fas fa-paper-plane me-2"></i>تقديم الطلب';
            submitButton.disabled = false;
        }
    }
}

// دالة لتقديم طلب إجازة جديد
async function submitVacationRequest() {
    try {
        console.log('بدء تقديم طلب إجازة جديد');
        const form = document.getElementById('vacationForm');

        if (!form.checkValidity()) {
            console.log('النموذج غير صالح، عرض رسائل التحقق');
            form.classList.add('was-validated');
            return;
        }

        // الحصول على بيانات النموذج
        const managerSelect = form.querySelector('select[name="fullName"]');
        const managerId = managerSelect.value;

        // التحقق من اختيار المدير
        if (!managerId) {
            console.error('لم يتم اختيار المدير');
            showAlert('يرجى اختيار المدير', 'danger');
            return;
        }

        console.log('المدير المختار:', managerId);

        const vacationType = form.querySelector('select[name="vacationType"]').value;
        const duration = form.querySelector('input[name="duration"]').value;
        const startDate = form.querySelector('input[name="startDate"]').value;
        const endDate = form.querySelector('input[name="endDate"]').value;
        const deputyName = form.querySelector('input[name="deputyName"]').value;
        const deputyPosition = form.querySelector('input[name="deputyPosition"]').value;
        const deputyPhone = form.querySelector('input[name="deputyPhone"]').value;

        // إنشاء كائن الطلب للإرسال إلى الخادم
        const requestData = {
            manager_id: parseInt(managerId),
            vacation_type: vacationType,
            duration: parseInt(duration),
            start_date: startDate,
            end_date: endDate,
            deputy_name: deputyName,
            deputy_position: deputyPosition,
            deputy_phone: deputyPhone
        };

        console.log('بيانات الطلب المراد إرسالها:', requestData);

        // عرض مؤشر التحميل
        const submitButton = form.querySelector('button[type="submit"]');
        const originalButtonText = submitButton.innerHTML;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري الإرسال...';
        submitButton.disabled = true;

        // إرسال الطلب إلى الخادم
        const response = await fetch('/api/vacation-requests', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        console.log('استجابة الخادم:', response.status, response.statusText);

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'فشل في إرسال طلب الإجازة');
        }

        const data = await response.json();
        console.log('بيانات الاستجابة:', data);

        // إعادة تعيين النموذج
        form.reset();
        form.classList.remove('was-validated');

        // عرض رسالة نجاح
        showAlert('تم تقديم طلب الإجازة بنجاح', 'success');

        // العودة إلى واجهة إدخال الطلبات
        showRequestsEntry();

        // تحديث جدول الطلبات
        updateRequestsEntryTable();
    } catch (error) {
        console.error('خطأ في تقديم طلب الإجازة:', error);
        showAlert('حدث خطأ في تقديم طلب الإجازة: ' + error.message, 'danger');
    } finally {
        // إعادة زر التقديم إلى حالته الأصلية
        const submitButton = document.querySelector('#vacationForm button[type="submit"]');
        if (submitButton) {
            submitButton.innerHTML = '<i class="fas fa-paper-plane me-2"></i>تقديم الطلب';
            submitButton.disabled = false;
        }
    }
}

// دالة لتحديث جدول الطلبات
async function updateRequestsTable() {
    try {
        console.log('تحديث جدول الطلبات في الصفحة الرئيسية');
        const tableBody = document.querySelector('#requestsTable tbody');
        if (!tableBody) {
            console.error('لم يتم العثور على جدول الطلبات');
            return;
        }

        // عرض رسالة تحميل
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل البيانات من قاعدة البيانات...</p>
                </td>
            </tr>
        `;

        // جلب البيانات من الخادم باستخدام API Client
        const [travelRequests, vacationRequests] = await Promise.all([
            apiClient.getTravelRequests(),
            apiClient.getVacationRequests()
        ]);

        console.log('عدد طلبات السفر من الخادم:', travelRequests.length);
        console.log('عدد طلبات الإجازة من الخادم:', vacationRequests.length);

        // تحديث عدادات الطلبات
        await updateRequestsCounters();

        // تحويل طلبات السفر إلى التنسيق المطلوب
        const formattedTravelRequests = travelRequests.map(request => ({
            id: request.id,
            type: 'travel',
            managerName: request.manager_name || request.fullName,
            position: request.manager_position || request.position,
            createdAt: request.created_at || request.submissionDate,
            departureDate: request.departure_date || request.departureDate,
            returnDate: request.return_date || request.returnDate,
            startDate: request.departure_date || request.departureDate,
            endDate: request.return_date || request.returnDate
        }));

        // تحويل طلبات الإجازة إلى التنسيق المطلوب
        const formattedVacationRequests = vacationRequests.map(request => ({
            id: request.id,
            type: 'vacation',
            managerName: request.manager_name || request.fullName,
            position: request.manager_position || request.position,
            createdAt: request.created_at || request.submissionDate,
            startDate: request.start_date || request.startDate,
            endDate: request.end_date || request.endDate
        }));

        // دمج الطلبات
        const allRequests = [...formattedTravelRequests, ...formattedVacationRequests];

        // ترتيب الطلبات حسب التاريخ (الأحدث أولاً)
        allRequests.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        console.log('إجمالي عدد الطلبات:', allRequests.length);

        // عرض الطلبات في الجدول
        displayRequestsInTable(allRequests, tableBody);

        console.log('تم تحديث جدول الطلبات بنجاح');

    } catch (error) {
        console.error('خطأ في تحديث جدول الطلبات:', error);
        showAlert('حدث خطأ في تحديث جدول الطلبات', 'danger');

        // في حالة فشل الاتصال بالخادم، استخدم البيانات المحلية
        try {
            const travelRequests = JSON.parse(localStorage.getItem('travelRequests') || '[]');
            const vacationRequests = JSON.parse(localStorage.getItem('vacationRequests') || '[]');

            console.log('استخدام البيانات المحلية. عدد طلبات السفر:', travelRequests.length);
            console.log('استخدام البيانات المحلية. عدد طلبات الإجازة:', vacationRequests.length);

            const tableBody = document.querySelector('#requestsTable tbody');
            if (!tableBody) return;

            // تحويل طلبات السفر إلى التنسيق المطلوب
            const formattedTravelRequests = travelRequests.map(request => ({
                id: request.id,
                type: 'travel',
                managerName: request.fullName,
                position: request.position,
                createdAt: request.submissionDate,
                departureDate: request.departureDate,
                returnDate: request.returnDate,
                startDate: request.departureDate,
                endDate: request.returnDate
            }));

            // تحويل طلبات الإجازة إلى التنسيق المطلوب
            const formattedVacationRequests = vacationRequests.map(request => ({
                id: request.id,
                type: 'vacation',
                managerName: request.fullName,
                position: request.position,
                createdAt: request.submissionDate,
                startDate: request.startDate,
                endDate: request.endDate
            }));

            // دمج الطلبات
            const allRequests = [...formattedTravelRequests, ...formattedVacationRequests];

            // ترتيب الطلبات حسب التاريخ (الأحدث أولاً)
            allRequests.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

            // عرض الطلبات في الجدول
            displayRequestsInTable(allRequests, tableBody);
        } catch (fallbackError) {
            console.error('خطأ في استخدام البيانات المحلية:', fallbackError);
        }
    }
}

// دالة لعرض الطلبات في الجدول
function displayRequestsInTable(requests, tableBody) {
    // تفريغ الجدول
    tableBody.innerHTML = '';

    if (requests.length > 0) {
        // ترتيب الطلبات من الأحدث إلى الأقدم
        requests.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        // تحديث الجدول
        requests.forEach(request => {
            const row = document.createElement('tr');

            // تحديد نوع الطلب ورمزه
            let requestTypeIcon, requestTypeText, startDate, endDate;

            if (request.type === 'travel') {
                requestTypeIcon = 'fa-plane';
                requestTypeText = 'طلب سفر';
                startDate = request.departureDate;
                endDate = request.returnDate;
            } else {
                requestTypeIcon = 'fa-calendar-alt';
                requestTypeText = 'طلب إجازة';
                startDate = request.startDate;
                endDate = request.endDate;
            }

            // تنسيق التواريخ
            const formatDate = (dateString) => {
                if (!dateString) return '';
                try {
                    return new Date(dateString).toLocaleDateString('ar-SA');
                } catch (e) {
                    return dateString;
                }
            };

            row.innerHTML = `
                <td>#${request.id}</td>
                <td><i class="fas ${requestTypeIcon} text-primary me-2"></i>${requestTypeText}</td>
                <td>${request.managerName || ''}</td>
                <td>${request.position || ''}</td>
                <td>${formatDate(request.createdAt)}</td>
                <td>${formatDate(startDate)}</td>
                <td>${formatDate(endDate)}</td>
                <td>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-info" onclick="showRequestDetails(${request.id}, '${request.type}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="editRequest(${request.id}, '${request.type}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteRequest(${request.id}, '${request.type}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;

            tableBody.appendChild(row);
        });

    } else {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">لا توجد طلبات مسجلة</td>
            </tr>
        `;
    }
}

// دالة لتحديث عدادات الطلبات في الصفحة الرئيسية
async function updateRequestsCounters() {
    try {
        console.log('تحديث عدادات الطلبات من قاعدة البيانات');

        // عرض مؤشرات التحميل
        const travelCountElement = document.getElementById('travelRequestsCount');
        const vacationCountElement = document.getElementById('vacationRequestsCount');

        if (travelCountElement && travelCountElement.textContent === '0') {
            travelCountElement.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
        }

        if (vacationCountElement && vacationCountElement.textContent === '0') {
            vacationCountElement.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
        }

        // محاولة جلب البيانات من الخادم باستخدام API Client
        try {
            // جلب البيانات من الخادم باستخدام API Client
            const [travelRequests, vacationRequests] = await Promise.all([
                apiClient.getTravelRequests(),
                apiClient.getVacationRequests()
            ]);

            const travelCount = travelRequests.length;
            const vacationCount = vacationRequests.length;

            console.log(`عدد طلبات السفر: ${travelCount}, عدد طلبات الإجازة: ${vacationCount}`);

            // تحديث عداد طلبات السفر
            if (travelCountElement) {
                travelCountElement.textContent = travelCount;
            }

            // تحديث عداد طلبات الإجازة
            if (vacationCountElement) {
                vacationCountElement.textContent = vacationCount;
            }

            // لا نستخدم التخزين المحلي بعد الآن

            return true;
        } catch (apiError) {
            console.error('خطأ في جلب البيانات من API:', apiError);
            throw apiError; // إعادة رمي الخطأ للمعالجة في الـ catch الخارجي
        }
    } catch (error) {
        console.error('خطأ في تحديث عدادات الطلبات:', error);

        // في حالة فشل الاتصال بالخادم، نعرض رسالة خطأ
        try {
            console.log('فشل في الاتصال بالخادم لتحديث العدادات');

            // تحديث عداد طلبات السفر ليظهر 0
            const travelCountElement = document.getElementById('travelRequestsCount');
            if (travelCountElement) {
                travelCountElement.textContent = '0';
            }

            // تحديث عداد طلبات الإجازة ليظهر 0
            const vacationCountElement = document.getElementById('vacationRequestsCount');
            if (vacationCountElement) {
                vacationCountElement.textContent = '0';
            }

            console.log('تم تحديث العدادات بقيم افتراضية');
            return true;
        } catch (localError) {
            console.error('خطأ في استخدام البيانات المحلية:', localError);

            // في حالة فشل كل المحاولات، عرض علامة الخطأ
            const travelCountElement = document.getElementById('travelRequestsCount');
            const vacationCountElement = document.getElementById('vacationRequestsCount');

            if (travelCountElement && travelCountElement.textContent === '0') {
                travelCountElement.textContent = '!';
                travelCountElement.title = 'فشل في جلب البيانات من قاعدة البيانات';
            }

            if (vacationCountElement && vacationCountElement.textContent === '0') {
                vacationCountElement.textContent = '!';
                vacationCountElement.title = 'فشل في جلب البيانات من قاعدة البيانات';
            }

            return false;
        }
    }
}

// دالة لتحديث جدول الطلبات في واجهة إدخال الطلبات
function updateRequestsEntryTable() {
    try {
        console.log('تحديث جدول الطلبات في واجهة إدخال الطلبات');

        const tableBody = document.querySelector('#requestsEntryTable tbody');
        if (!tableBody) {
            console.error('لم يتم العثور على جدول الطلبات في واجهة إدخال الطلبات');
            return;
        }

        // عرض رسالة تحميل
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل البيانات من الخادم...</p>
                </td>
            </tr>
        `;

        // جلب طلبات السفر من الخادم
        fetch('/api/travel-requests')
            .then(response => {
                if (!response.ok) {
                    throw new Error('فشل في جلب طلبات السفر من الخادم');
                }
                return response.json();
            })
            .then(travelData => {
                // جلب طلبات الإجازة من الخادم
                return fetch('/api/vacation-requests')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('فشل في جلب طلبات الإجازة من الخادم');
                        }
                        return response.json();
                    })
                    .then(vacationData => {
                        // استخراج البيانات من الاستجابة
                        const travelRequests = travelData.data || [];
                        const vacationRequests = vacationData.data || [];

                        console.log('عدد طلبات السفر من الخادم:', travelRequests.length);
                        console.log('عدد طلبات الإجازة من الخادم:', vacationRequests.length);

                        // تنسيق التواريخ
                        const formatDate = (dateString) => {
                            if (!dateString) return '-';
                            try {
                                return new Date(dateString).toLocaleDateString('ar-SA');
                            } catch (e) {
                                return dateString;
                            }
                        };

                        // تحويل طلبات السفر إلى التنسيق المطلوب
                        const formattedTravelRequests = travelRequests.map(request => ({
                            id: request.id,
                            type: 'travel',
                            managerName: request.manager_name || request.fullName,
                            position: request.position || request.manager_position,
                            department: request.department || request.manager_department,
                            createdAt: request.created_at || request.submissionDate,
                            startDate: request.departure_date || request.departureDate,
                            endDate: request.return_date || request.returnDate,
                            travelCountry: request.travel_country || request.travelCountry,
                            travelPurpose: request.travel_purpose || request.travelPurpose,
                            deputyName: request.deputy_name || request.deputyName,
                            deputyPosition: request.deputy_position || request.deputyPosition
                        }));

                        // تحويل طلبات الإجازة إلى التنسيق المطلوب
                        const formattedVacationRequests = vacationRequests.map(request => ({
                            id: request.id,
                            type: 'vacation',
                            managerName: request.manager_name || request.fullName,
                            position: request.position || request.manager_position,
                            department: request.department || request.manager_department,
                            createdAt: request.created_at || request.submissionDate,
                            startDate: request.start_date || request.startDate,
                            endDate: request.end_date || request.endDate,
                            vacationType: request.vacation_type || request.vacationType,
                            deputyName: request.deputy_name || request.deputyName,
                            deputyPosition: request.deputy_position || request.deputyPosition
                        }));

                        // دمج الطلبات
                        const allRequests = [...formattedTravelRequests, ...formattedVacationRequests];

                        // ترتيب الطلبات حسب التاريخ (الأحدث أولاً)
                        allRequests.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

                        if (allRequests.length > 0) {
                            // إنشاء صفوف الجدول
                            const rows = allRequests.map(request => {
                                // تحديد نوع الطلب ورمزه
                                const isVacation = request.type === 'vacation';
                                const icon = isVacation ? 'fa-calendar' : 'fa-plane';
                                const iconColor = isVacation ? 'success' : 'primary';
                                const requestTypeText = isVacation ? 'طلب إجازة' : 'طلب سفر';

                                // تحديد معلومات إضافية للعرض في الجدول
                                const additionalInfo = isVacation
                                    ? `<span class="badge bg-success">${getVacationTypeName(request.vacationType)}</span>`
                                    : `<span class="badge bg-info">${request.travelCountry || 'غير محدد'}</span>`;

                                return `
                                <tr>
                                    <td>#${request.id}</td>
                                    <td>
                                        <i class="fas ${icon} text-${iconColor} me-2"></i>
                                        ${requestTypeText}
                                        <br>
                                        <small>${additionalInfo}</small>
                                    </td>
                                    <td>${request.managerName || '-'}</td>
                                    <td>${request.position || '-'}</td>
                                    <td>${formatDate(request.createdAt)}</td>
                                    <td>${formatDate(request.startDate)}</td>
                                    <td>${formatDate(request.endDate)}</td>
                                    <td>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-info" title="عرض التفاصيل" onclick="showRequestDetails(${request.id}, '${request.type}')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-primary" title="تعديل" onclick="editRequest(${request.id}, '${request.type}')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" title="حذف" onclick="deleteRequest(${request.id}, '${request.type}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                `;
                            }).join('');

                            tableBody.innerHTML = rows;
                        } else {
                            tableBody.innerHTML = `
                                <tr>
                                    <td colspan="8" class="text-center">لا توجد طلبات مسجلة</td>
                                </tr>
                            `;
                        }
                    })
                    .catch(error => {
                        console.error('خطأ في جلب بيانات الطلبات من الخادم:', error);

                        // في حالة فشل الاتصال بالخادم، استخدم البيانات المحلية
                        const travelRequests = JSON.parse(localStorage.getItem('travelRequests') || '[]');
                        const vacationRequests = JSON.parse(localStorage.getItem('vacationRequests') || '[]');

                        console.log('استخدام البيانات المحلية كبديل. عدد طلبات السفر:', travelRequests.length);
                        console.log('استخدام البيانات المحلية كبديل. عدد طلبات الإجازة:', vacationRequests.length);

                        // تنسيق التواريخ
                        const formatDate = (dateString) => {
                            if (!dateString) return '-';
                            try {
                                return new Date(dateString).toLocaleDateString('ar-SA');
                            } catch (e) {
                                return dateString;
                            }
                        };

                        // تحويل طلبات السفر إلى التنسيق المطلوب
                        const formattedTravelRequests = travelRequests.map(request => ({
                            id: request.id,
                            type: 'travel',
                            managerName: request.fullName,
                            position: request.position,
                            department: request.department,
                            createdAt: request.submissionDate,
                            startDate: request.departureDate,
                            endDate: request.returnDate,
                            travelCountry: request.travelCountry,
                            travelPurpose: request.travelPurpose,
                            deputyName: request.deputyName,
                            deputyPosition: request.deputyPosition
                        }));

                        // تحويل طلبات الإجازة إلى التنسيق المطلوب
                        const formattedVacationRequests = vacationRequests.map(request => ({
                            id: request.id,
                            type: 'vacation',
                            managerName: request.fullName,
                            position: request.position,
                            department: request.department,
                            createdAt: request.submissionDate,
                            startDate: request.startDate,
                            endDate: request.endDate,
                            vacationType: request.vacationType,
                            deputyName: request.deputyName,
                            deputyPosition: request.deputyPosition
                        }));

                        // دمج الطلبات
                        const allRequests = [...formattedTravelRequests, ...formattedVacationRequests];

                        // ترتيب الطلبات حسب التاريخ (الأحدث أولاً)
                        allRequests.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

                        if (allRequests.length > 0) {
                            // إنشاء صفوف الجدول
                            const rows = allRequests.map(request => {
                                // تحديد نوع الطلب ورمزه
                                const isVacation = request.type === 'vacation' || request.requestType === 'vacation';
                                const icon = isVacation ? 'fa-calendar' : 'fa-plane';
                                const iconColor = isVacation ? 'success' : 'primary';
                                const requestTypeText = isVacation ? 'طلب إجازة' : 'طلب سفر';
                                const requestType = isVacation ? 'vacation' : 'travel';

                                // تحديد معلومات إضافية للعرض في الجدول
                                const additionalInfo = isVacation
                                    ? `<span class="badge bg-success">${getVacationTypeName(request.vacationType)}</span>`
                                    : `<span class="badge bg-info">${request.travelCountry || 'غير محدد'}</span>`;

                                return `
                                <tr>
                                    <td>#${request.id}</td>
                                    <td>
                                        <i class="fas ${icon} text-${iconColor} me-2"></i>
                                        ${requestTypeText}
                                        <br>
                                        <small>${additionalInfo}</small>
                                    </td>
                                    <td>${request.managerName || '-'}</td>
                                    <td>${request.position || '-'}</td>
                                    <td>${formatDate(request.createdAt)}</td>
                                    <td>${formatDate(request.startDate)}</td>
                                    <td>${formatDate(request.endDate)}</td>
                                    <td>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-info" title="عرض التفاصيل" onclick="showRequestDetails(${request.id}, '${requestType}')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-primary" title="تعديل" onclick="editRequest(${request.id}, '${requestType}')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" title="حذف" onclick="deleteRequest(${request.id}, '${requestType}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                `;
                            }).join('');

                            tableBody.innerHTML = rows;
                        } else {
                            tableBody.innerHTML = `
                                <tr>
                                    <td colspan="8" class="text-center">لا توجد طلبات مسجلة</td>
                                </tr>
                            `;
                        }
                    });
            })
            .catch(error => {
                console.error('خطأ في جلب بيانات الطلبات من الخادم:', error);
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center text-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            حدث خطأ في الاتصال بالخادم: ${error.message}
                        </td>
                    </tr>
                `;
            });
    } catch (error) {
        console.error('خطأ في تحديث جدول الطلبات في واجهة إدخال الطلبات:', error);

        const tableBody = document.querySelector('#requestsEntryTable tbody');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        حدث خطأ في تحميل البيانات: ${error.message}
                    </td>
                </tr>
            `;
        }
    }
}

// دالة لتحديث لوحات المعلومات في الصفحة الرئيسية
async function updateDashboardStats() {
    try {
        console.log('تحديث لوحات المعلومات في الصفحة الرئيسية');

        // عرض مؤشرات التحميل
        const travelRequestsCountElement = document.getElementById('travelRequestsCount');
        const vacationRequestsCountElement = document.getElementById('vacationRequestsCount');
        const managersCountElement = document.getElementById('managersCount');

        if (travelRequestsCountElement) {
            travelRequestsCountElement.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
        }

        if (vacationRequestsCountElement) {
            vacationRequestsCountElement.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
        }

        if (managersCountElement) {
            managersCountElement.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
        }

        // جلب إحصائيات لوحة المعلومات باستخدام apiClient
        console.log('استدعاء apiClient.getDashboardStats()');
        const stats = await apiClient.getDashboardStats();

        console.log('تم جلب إحصائيات لوحة المعلومات بنجاح:', stats);

        // تحديث عدادات لوحة المعلومات
        if (travelRequestsCountElement) {
            travelRequestsCountElement.textContent = stats.travel.total || 0;
        }

        if (vacationRequestsCountElement) {
            vacationRequestsCountElement.textContent = stats.vacation.total || 0;
        }

        if (managersCountElement) {
            managersCountElement.textContent = stats.managers.total || 0;
        }

        // محاولة تحديث العدادات باستخدام updateRequestsCounters كاحتياط إضافي
        updateRequestsCounters();

    } catch (error) {
        console.error('خطأ في تحديث لوحات المعلومات:', error);

        // في حالة فشل الاتصال بالخادم، نعرض قيم افتراضية
        try {
            console.log('فشل في الاتصال بالخادم لتحديث لوحة المعلومات');

            // عرض قيم افتراضية
            const travelRequestsCountElement = document.getElementById('travelRequestsCount');
            const vacationRequestsCountElement = document.getElementById('vacationRequestsCount');
            const managersCountElement = document.getElementById('managersCount');

            if (travelRequestsCountElement) {
                travelRequestsCountElement.textContent = '0';
            }

            if (vacationRequestsCountElement) {
                vacationRequestsCountElement.textContent = '0';
            }

            if (managersCountElement) {
                managersCountElement.textContent = '0';
            }

            console.log('تم تحديث العدادات بقيم افتراضية');

            // محاولة تحديث العدادات باستخدام updateRequestsCounters كاحتياط إضافي
            updateRequestsCounters();

        } catch (localError) {
            console.error('خطأ في استخدام البيانات المحلية:', localError);

            // في حالة فشل كل المحاولات، عرض رسالة خطأ
            const travelRequestsCountElement = document.getElementById('travelRequestsCount');
            const vacationRequestsCountElement = document.getElementById('vacationRequestsCount');
            const managersCountElement = document.getElementById('managersCount');

            if (travelRequestsCountElement) {
                travelRequestsCountElement.textContent = '!';
                travelRequestsCountElement.title = 'فشل في جلب البيانات من قاعدة البيانات';
            }

            if (vacationRequestsCountElement) {
                vacationRequestsCountElement.textContent = '!';
                vacationRequestsCountElement.title = 'فشل في جلب البيانات من قاعدة البيانات';
            }

            if (managersCountElement) {
                managersCountElement.textContent = '!';
                managersCountElement.title = 'فشل في جلب البيانات من قاعدة البيانات';
            }

            // عرض رسالة خطأ للمستخدم
            showToast('خطأ في جلب البيانات', 'فشل في جلب إحصائيات لوحة المعلومات من قاعدة البيانات. يرجى التحقق من اتصالك بالخادم وإعادة المحاولة.', 'error');
        }
    }
}

// دالة لتحديث جدول المدراء المسافرين حاليا
async function updateTravelingManagersTable() {
    try {
        console.log('تحديث جداول المدراء الغائبين');

        // عرض مؤشرات التحميل
        const travelingCountElement = document.getElementById('travelingManagersCount');
        const vacationingCountElement = document.getElementById('vacationingManagersCount');

        if (travelingCountElement) {
            travelingCountElement.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
        }

        if (vacationingCountElement) {
            vacationingCountElement.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
        }

        // تحديث لوحات المعلومات في الصفحة الرئيسية
        await updateDashboardStats();

        // تحديث جدول المدراء المسافرين
        const travelingResult = await updateTravelingTab();

        // تحديث جدول المدراء في إجازة
        const vacationingResult = await updateVacationingTab();

        // تحديث جدول المدراء الأكثر سفراً
        await updateMostTravelingManagersTable();

        // تحديث جدول المدراء الأكثر إجازة
        await updateMostVacationingManagersTable();

        // تحديث عدادات المدراء الغائبين في الصفحة الرئيسية
        updateAbsentManagersCounters();

        console.log('تم تحديث جداول المدراء الغائبين بنجاح');
        return true;
    } catch (error) {
        console.error('خطأ في تحديث جداول المدراء الغائبين:', error);

        // محاولة تحديث العدادات على الأقل
        try {
            updateAbsentManagersCounters();
        } catch (counterError) {
            console.error('خطأ في تحديث عدادات المدراء الغائبين:', counterError);
        }

        return false;
    }
}

// دالة لتحديث عدادات المدراء الغائبين في الصفحة الرئيسية
async function updateAbsentManagersCounters() {
    try {
        console.log('تحديث عدادات المدراء الغائبين في الصفحة الرئيسية');

        // جلب البيانات من قاعدة البيانات
        try {
            // جلب بيانات المدراء المسافرين
            const travelingManagers = await apiClient.getCurrentlyTravelingManagers();

            // تحديث عداد المدراء المسافرين
            const travelingCountElement = document.getElementById('travelingManagersCount');
            if (travelingCountElement) {
                travelingCountElement.textContent = travelingManagers.length;
            }

            // جلب بيانات المدراء في إجازة
            const vacationingManagers = await apiClient.getCurrentlyVacationingManagers();

            // تحديث عداد المدراء في إجازة
            const vacationingCountElement = document.getElementById('vacationingManagersCount');
            if (vacationingCountElement) {
                vacationingCountElement.textContent = vacationingManagers.length;
            }
        } catch (error) {
            console.error('خطأ في جلب بيانات المدراء الغائبين:', error);

            // في حالة الخطأ، نعرض 0
            const travelingCountElement = document.getElementById('travelingManagersCount');
            if (travelingCountElement) {
                travelingCountElement.textContent = '0';
            }

            const vacationingCountElement = document.getElementById('vacationingManagersCount');
            if (vacationingCountElement) {
                vacationingCountElement.textContent = '0';
            }
        }

        // تحديث العداد الإجمالي للمدراء الغائبين
        const absentManagersCountElement = document.getElementById('absentManagersCount');
        if (absentManagersCountElement) {
            const travelingCount = parseInt(document.getElementById('travelingManagersCount')?.textContent || '0');
            const vacationingCount = parseInt(document.getElementById('vacationingManagersCount')?.textContent || '0');
            absentManagersCountElement.textContent = travelingCount + vacationingCount;
        }

        console.log('تم تحديث عدادات المدراء الغائبين بنجاح');
        return true;
    } catch (error) {
        console.error('خطأ في تحديث عدادات المدراء الغائبين:', error);
        return false;
    }
}

// متغيرات عالمية للرسوم البيانية
let travelChart = null;
let vacationChart = null;

// دالة لتحديث بيانات المدراء الأكثر سفراً
async function updateMostTravelingManagersTable() {
    try {
        console.log('تحديث بيانات المدراء الأكثر سفراً من قاعدة البيانات');
        const container = document.getElementById('mostTravelingManagersList');
        if (!container) {
            console.error('لم يتم العثور على حاوية المدراء الأكثر سفراً');
            return;
        }

        // عرض رسالة التحميل
        container.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل بيانات المدراء الأكثر سفراً من قاعدة البيانات...</p>
            </div>
        `;

        // جلب بيانات المدراء الأكثر سفراً من الخادم
        const topManagers = await apiClient.getMostTravelingManagers(5);

        console.log('عدد المدراء الأكثر سفراً من الخادم:', topManagers.length);
        console.log('بيانات المدراء الأكثر سفراً من الخادم:', JSON.stringify(topManagers));

        if (topManagers.length > 0) {
            // عرض بيانات المدراء الأكثر سفراً
            displayTravelingManagersData(container, topManagers);
        } else {
            container.innerHTML = `
                <div class="text-center py-4">
                    <p>لا توجد بيانات متاحة للمدراء الأكثر سفراً</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('خطأ في تحديث بيانات المدراء الأكثر سفراً:', error);
        container.innerHTML = `
            <div class="text-center py-4">
                <p class="text-danger">حدث خطأ أثناء تحميل بيانات المدراء الأكثر سفراً. يرجى التحقق من اتصالك بالخادم وإعادة المحاولة.</p>
            </div>
        `;
    }
}

// دالة لتحديث بيانات المدراء الأكثر إجازة
async function updateMostVacationingManagersTable() {
    try {
        console.log('تحديث بيانات المدراء الأكثر إجازة من قاعدة البيانات');
        const container = document.getElementById('mostVacationingManagersList');
        if (!container) {
            console.error('لم يتم العثور على حاوية المدراء الأكثر إجازة');
            return;
        }

        // عرض رسالة التحميل
        container.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل بيانات المدراء الأكثر إجازة من قاعدة البيانات...</p>
            </div>
        `;

        // جلب بيانات المدراء الأكثر إجازة من الخادم
        const topManagers = await apiClient.getMostVacationingManagers(5);

        console.log('عدد المدراء الأكثر إجازة من الخادم:', topManagers.length);
        console.log('بيانات المدراء الأكثر إجازة من الخادم:', JSON.stringify(topManagers));

        if (topManagers.length > 0) {
            // عرض بيانات المدراء الأكثر إجازة
            displayVacationingManagersData(container, topManagers);
        } else {
            container.innerHTML = `
                <div class="text-center py-4">
                    <p>لا توجد بيانات متاحة للمدراء الأكثر إجازة</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('خطأ في تحديث بيانات المدراء الأكثر إجازة:', error);
        container.innerHTML = `
            <div class="text-center py-4">
                <p class="text-danger">حدث خطأ أثناء تحميل بيانات المدراء الأكثر إجازة. يرجى التحقق من اتصالك بالخادم وإعادة المحاولة.</p>
            </div>
        `;
    }
}

// دالة لعرض بيانات المدراء الأكثر سفراً
function displayTravelingManagersData(container, managers) {
    container.innerHTML = `
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>المنصب</th>
                        <th>عدد السفريات</th>
                    </tr>
                </thead>
                <tbody>
                    ${managers.map(manager => `
                        <tr>
                            <td>${manager.name}</td>
                            <td>${manager.position}</td>
                            <td>${manager.travel_count}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

// دالة لعرض بيانات المدراء الأكثر إجازة
function displayVacationingManagersData(container, managers) {
    container.innerHTML = `
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>المنصب</th>
                        <th>عدد الإجازات</th>
                    </tr>
                </thead>
                <tbody>
                    ${managers.map(manager => `
                        <tr>
                            <td>${manager.name}</td>
                            <td>${manager.position}</td>
                            <td>${manager.vacation_count}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

// دالة لعرض نافذة استيراد المدراء
function showImportManagersModal() {
    const modal = new bootstrap.Modal(document.getElementById('importManagersModal'));
    modal.show();
}

// دالة لاستيراد بيانات المدراء من ملف Excel
function importManagersFromExcel() {
    const fileInput = document.getElementById('excelFileInput');

    if (!fileInput.files.length) {
        showAlert('يرجى اختيار ملف Excel أولاً', 'danger');
        return;
    }

    const file = fileInput.files[0];
    const reader = new FileReader();

    reader.onload = function(e) {
        try {
            // قراءة ملف Excel
            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, { type: 'array' });

            // الحصول على الورقة الأولى
            const firstSheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[firstSheetName];

            // تحويل البيانات إلى مصفوفة من الكائنات
            const jsonData = XLSX.utils.sheet_to_json(worksheet);

            if (jsonData.length === 0) {
                showAlert('لم يتم العثور على بيانات في الملف', 'warning');
                return;
            }

            // تحويل البيانات إلى تنسيق متوافق مع النظام
            const managers = jsonData.map((row, index) => {
                return {
                    id: Date.now() + index, // إنشاء معرف فريد
                    full_name: row['الاسم الكامل'] || row['الاسم'] || '',
                    current_title: row['المسمى الحالي'] || row['المنصب'] || '',
                    main_workplace: row['القسم'] || row['الإدارة'] || '',
                    education_degree: row['المؤهل العلمي'] || row['المؤهل'] || '',
                    contact_numbers: row['رقم الهاتف'] || row['الهاتف'] || '',
                    email: row['البريد الإلكتروني'] || row['البريد'] || '',
                    employment_date: row['تاريخ التعيين'] || ''
                };
            });

            // الحصول على المدراء الحاليين
            const existingManagers = JSON.parse(localStorage.getItem('managers') || '[]');

            // دمج المدراء الجدد مع الحاليين
            const allManagers = [...existingManagers, ...managers];

            // حفظ البيانات في localStorage
            localStorage.setItem('managers', JSON.stringify(allManagers));

            // تحديث جدول المدراء
            updateManagersTable();

            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.getElementById('importManagersModal'));
            modal.hide();

            // إعادة تعيين النموذج
            document.getElementById('importManagersForm').reset();

            // عرض رسالة نجاح
            showAlert(`تم استيراد ${managers.length} مدير بنجاح`, 'success');

        } catch (error) {
            console.error('خطأ في استيراد البيانات:', error);
            showAlert('حدث خطأ أثناء استيراد البيانات. يرجى التأكد من تنسيق الملف.', 'danger');
        }
    };

    reader.onerror = function() {
        showAlert('حدث خطأ أثناء قراءة الملف', 'danger');
    };

    reader.readAsArrayBuffer(file);
}

// دالة لتصدير بيانات المدراء إلى ملف Excel
function exportManagersToExcel() {
    try {
        // الحصول على بيانات المدراء
        const managers = JSON.parse(localStorage.getItem('managers') || '[]');

        if (managers.length === 0) {
            showAlert('لا توجد بيانات مدراء للتصدير', 'warning');
            return;
        }

        // تحويل البيانات إلى تنسيق مناسب للتصدير
        const exportData = managers.map(manager => {
            return {
                'الاسم الكامل': manager.full_name || '',
                'المسمى الحالي': manager.current_title || '',
                'القسم': manager.main_workplace || '',
                'المؤهل العلمي': manager.education_degree || '',
                'رقم الهاتف': manager.contact_numbers || '',
                'البريد الإلكتروني': manager.email || '',
                'تاريخ التعيين': manager.employment_date || ''
            };
        });

        // إنشاء ورقة عمل جديدة
        const worksheet = XLSX.utils.json_to_sheet(exportData);

        // إنشاء مصنف عمل جديد وإضافة ورقة العمل إليه
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'المدراء');

        // تصدير المصنف إلى ملف Excel
        const today = new Date().toISOString().slice(0, 10);
        XLSX.writeFile(workbook, `بيانات_المدراء_${today}.xlsx`);

        // عرض رسالة نجاح
        showAlert('تم تصدير بيانات المدراء بنجاح', 'success');

    } catch (error) {
        console.error('خطأ في تصدير البيانات:', error);
        showAlert('حدث خطأ أثناء تصدير البيانات', 'danger');
    }
}

// دالة لتقديم طلب سفر جديد
async function submitTravelRequest() {
    try {
        console.log('بدء تقديم طلب سفر جديد');
        const form = document.getElementById('travelForm');

        if (!form.checkValidity()) {
            console.log('النموذج غير صالح، عرض رسائل التحقق');
            form.classList.add('was-validated');
            return;
        }

        // الحصول على بيانات النموذج
        const managerSelect = form.querySelector('select[name="fullName"]');
        const managerId = managerSelect.value;

        // التحقق من اختيار المدير
        if (!managerId) {
            console.error('لم يتم اختيار المدير');
            showAlert('يرجى اختيار المدير', 'danger');
            return;
        }

        console.log('المدير المختار:', managerId);

        const travelCountry = form.querySelector('select[name="travelCountry"]').value;
        const notificationDate = form.querySelector('input[name="notificationDate"]').value;
        const departureDate = form.querySelector('input[name="departureDate"]').value;
        const returnDate = form.querySelector('input[name="returnDate"]').value;
        const travelType = form.querySelector('select[name="travelType"]').value;
        const travelPurpose = form.querySelector('textarea[name="travelPurpose"]').value;
        const deputyName = form.querySelector('input[name="deputyName"]').value;
        const deputyPosition = form.querySelector('input[name="deputyPosition"]').value;
        const deputyPhone = form.querySelector('input[name="deputyPhone"]').value;

        // إنشاء كائن الطلب للإرسال إلى الخادم
        const requestData = {
            manager_id: parseInt(managerId),
            travel_country: travelCountry,
            notification_date: notificationDate,
            departure_date: departureDate,
            return_date: returnDate,
            travel_type: travelType,
            travel_purpose: travelPurpose,
            deputy_name: deputyName,
            deputy_position: deputyPosition,
            deputy_phone: deputyPhone
        };

        console.log('بيانات الطلب المراد إرسالها:', requestData);

        // عرض مؤشر التحميل
        const submitButton = form.querySelector('button[type="submit"]');
        const originalButtonText = submitButton.innerHTML;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري الإرسال...';
        submitButton.disabled = true;

        // إرسال الطلب إلى الخادم
        const response = await fetch('/api/travel-requests', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        console.log('استجابة الخادم:', response.status, response.statusText);

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'فشل في إرسال طلب السفر');
        }

        const data = await response.json();
        console.log('بيانات الاستجابة:', data);

        // إعادة تعيين النموذج
        form.reset();
        form.classList.remove('was-validated');

        // عرض رسالة نجاح
        showAlert('تم تقديم طلب السفر بنجاح', 'success');

        // العودة إلى واجهة إدخال الطلبات
        showRequestsEntry();

        // تحديث جدول الطلبات
        updateRequestsEntryTable();
    } catch (error) {
        console.error('خطأ في تقديم طلب السفر:', error);
        showAlert('حدث خطأ في تقديم طلب السفر: ' + error.message, 'danger');
    } finally {
        // إعادة زر التقديم إلى حالته الأصلية
        const submitButton = document.querySelector('#travelForm button[type="submit"]');
        if (submitButton) {
            submitButton.innerHTML = '<i class="fas fa-paper-plane me-2"></i>تقديم الطلب';
            submitButton.disabled = false;
        }
    }
}

// دالة لتقديم طلب إجازة جديد
async function submitVacationRequest() {
    try {
        console.log('بدء تقديم طلب إجازة جديد');
        const form = document.getElementById('vacationForm');

        if (!form.checkValidity()) {
            console.log('النموذج غير صالح، عرض رسائل التحقق');
            form.classList.add('was-validated');
            return;
        }

        // الحصول على بيانات النموذج
        const managerSelect = form.querySelector('select[name="fullName"]');
        const managerId = managerSelect.value;

        // التحقق من اختيار المدير
        if (!managerId) {
            console.error('لم يتم اختيار المدير');
            showAlert('يرجى اختيار المدير', 'danger');
            return;
        }

        console.log('المدير المختار:', managerId);

        const vacationType = form.querySelector('select[name="vacationType"]').value;
        const duration = form.querySelector('input[name="duration"]').value;
        const startDate = form.querySelector('input[name="startDate"]').value;
        const endDate = form.querySelector('input[name="endDate"]').value;
        const deputyName = form.querySelector('input[name="deputyName"]').value;
        const deputyPosition = form.querySelector('input[name="deputyPosition"]').value;
        const deputyPhone = form.querySelector('input[name="deputyPhone"]').value;

        // إنشاء كائن الطلب للإرسال إلى الخادم
        const requestData = {
            manager_id: parseInt(managerId),
            vacation_type: vacationType,
            duration: parseInt(duration),
            start_date: startDate,
            end_date: endDate,
            deputy_name: deputyName,
            deputy_position: deputyPosition,
            deputy_phone: deputyPhone
        };

        console.log('بيانات الطلب المراد إرسالها:', requestData);

        // عرض مؤشر التحميل
        const submitButton = form.querySelector('button[type="submit"]');
        const originalButtonText = submitButton.innerHTML;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري الإرسال...';
        submitButton.disabled = true;

        // إرسال الطلب إلى الخادم
        const response = await fetch('/api/vacation-requests', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        console.log('استجابة الخادم:', response.status, response.statusText);

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'فشل في إرسال طلب الإجازة');
        }

        const data = await response.json();
        console.log('بيانات الاستجابة:', data);

        // إعادة تعيين النموذج
        form.reset();
        form.classList.remove('was-validated');

        // عرض رسالة نجاح
        showAlert('تم تقديم طلب الإجازة بنجاح', 'success');

        // العودة إلى واجهة إدخال الطلبات
        showRequestsEntry();

        // تحديث جدول الطلبات
        updateRequestsEntryTable();
    } catch (error) {
        console.error('خطأ في تقديم طلب الإجازة:', error);
        showAlert('حدث خطأ في تقديم طلب الإجازة: ' + error.message, 'danger');
    } finally {
        // إعادة زر التقديم إلى حالته الأصلية
        const submitButton = document.querySelector('#vacationForm button[type="submit"]');
        if (submitButton) {
            submitButton.innerHTML = '<i class="fas fa-paper-plane me-2"></i>تقديم الطلب';
            submitButton.disabled = false;
        }
    }
}

// دالة لتحديث جدول الطلبات
async function updateRequestsTable() {
    try {
        console.log('تحديث جدول الطلبات في الصفحة الرئيسية');
        const tableBody = document.querySelector('#requestsTable tbody');
        if (!tableBody) {
            console.error('لم يتم العثور على جدول الطلبات');
            return;
        }

        // عرض رسالة تحميل
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل البيانات من قاعدة البيانات...</p>
                </td>
            </tr>
        `;

        // جلب البيانات من الخادم باستخدام API Client
        const [travelRequests, vacationRequests] = await Promise.all([
            apiClient.getTravelRequests(),
            apiClient.getVacationRequests()
        ]);

        console.log('عدد طلبات السفر من الخادم:', travelRequests.length);
        console.log('عدد طلبات الإجازة من الخادم:', vacationRequests.length);

        // تحديث عدادات الطلبات
        await updateRequestsCounters();

        // تحويل طلبات السفر إلى التنسيق المطلوب
        const formattedTravelRequests = travelRequests.map(request => ({
            id: request.id,
            type: 'travel',
            managerName: request.manager_name || request.fullName,
            position: request.manager_position || request.position,
            createdAt: request.created_at || request.submissionDate,
            departureDate: request.departure_date || request.departureDate,
            returnDate: request.return_date || request.returnDate,
            startDate: request.departure_date || request.departureDate,
            endDate: request.return_date || request.returnDate
        }));

        // تحويل طلبات الإجازة إلى التنسيق المطلوب
        const formattedVacationRequests = vacationRequests.map(request => ({
            id: request.id,
            type: 'vacation',
            managerName: request.manager_name || request.fullName,
            position: request.manager_position || request.position,
            createdAt: request.created_at || request.submissionDate,
            startDate: request.start_date || request.startDate,
            endDate: request.end_date || request.endDate
        }));

        // دمج الطلبات
        const allRequests = [...formattedTravelRequests, ...formattedVacationRequests];

        // ترتيب الطلبات حسب التاريخ (الأحدث أولاً)
        allRequests.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        console.log('إجمالي عدد الطلبات:', allRequests.length);

        // عرض الطلبات في الجدول
        displayRequestsInTable(allRequests, tableBody);

        console.log('تم تحديث جدول الطلبات بنجاح');

    } catch (error) {
        console.error('خطأ في تحديث جدول الطلبات:', error);
        showAlert('حدث خطأ في تحديث جدول الطلبات', 'danger');

        // في حالة فشل الاتصال بالخادم، استخدم البيانات المحلية
        try {
            const travelRequests = JSON.parse(localStorage.getItem('travelRequests') || '[]');
            const vacationRequests = JSON.parse(localStorage.getItem('vacationRequests') || '[]');

            console.log('استخدام البيانات المحلية. عدد طلبات السفر:', travelRequests.length);
            console.log('استخدام البيانات المحلية. عدد طلبات الإجازة:', vacationRequests.length);

            const tableBody = document.querySelector('#requestsTable tbody');
            if (!tableBody) return;

            // تحويل طلبات السفر إلى التنسيق المطلوب
            const formattedTravelRequests = travelRequests.map(request => ({
                id: request.id,
                type: 'travel',
                managerName: request.fullName,
                position: request.position,
                createdAt: request.submissionDate,
                departureDate: request.departureDate,
                returnDate: request.returnDate,
                startDate: request.departureDate,
                endDate: request.returnDate
            }));

            // تحويل طلبات الإجازة إلى التنسيق المطلوب
            const formattedVacationRequests = vacationRequests.map(request => ({
                id: request.id,
                type: 'vacation',
                managerName: request.fullName,
                position: request.position,
                createdAt: request.submissionDate,
                startDate: request.startDate,
                endDate: request.endDate
            }));

            // دمج الطلبات
            const allRequests = [...formattedTravelRequests, ...formattedVacationRequests];

            // ترتيب الطلبات حسب التاريخ (الأحدث أولاً)
            allRequests.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

            // عرض الطلبات في الجدول
            displayRequestsInTable(allRequests, tableBody);
        } catch (fallbackError) {
            console.error('خطأ في استخدام البيانات المحلية:', fallbackError);
        }
    }
}

// دالة لعرض الطلبات في الجدول
function displayRequestsInTable(requests, tableBody) {
    // تفريغ الجدول
    tableBody.innerHTML = '';

    if (requests.length > 0) {
        // ترتيب الطلبات من الأحدث إلى الأقدم
        requests.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        // تحديث الجدول
        requests.forEach(request => {
            const row = document.createElement('tr');

            // تحديد نوع الطلب ورمزه
            let requestTypeIcon, requestTypeText, startDate, endDate;

            if (request.type === 'travel') {
                requestTypeIcon = 'fa-plane';
                requestTypeText = 'طلب سفر';
                startDate = request.departureDate;
                endDate = request.returnDate;
            } else {
                requestTypeIcon = 'fa-calendar-alt';
                requestTypeText = 'طلب إجازة';
                startDate = request.startDate;
                endDate = request.endDate;
            }

            // تنسيق التواريخ
            const formatDate = (dateString) => {
                if (!dateString) return '';
                try {
                    return new Date(dateString).toLocaleDateString('ar-SA');
                } catch (e) {
                    return dateString;
                }
            };

            row.innerHTML = `
                <td>#${request.id}</td>
                <td><i class="fas ${requestTypeIcon} text-primary me-2"></i>${requestTypeText}</td>
                <td>${request.managerName || ''}</td>
                <td>${request.position || ''}</td>
                <td>${formatDate(request.createdAt)}</td>
                <td>${formatDate(startDate)}</td>
                <td>${formatDate(endDate)}</td>
                <td>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-info" onclick="showRequestDetails(${request.id}, '${request.type}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="editRequest(${request.id}, '${request.type}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteRequest(${request.id}, '${request.type}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;

            tableBody.appendChild(row);
        });

    } else {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">لا توجد طلبات مسجلة</td>
            </tr>
        `;
    }
}

// دالة لتحديث عدادات الطلبات في الصفحة الرئيسية
async function updateRequestsCounters() {
    try {
        console.log('تحديث عدادات الطلبات من قاعدة البيانات');

        // عرض مؤشرات التحميل
        const travelCountElement = document.getElementById('travelRequestsCount');
        const vacationCountElement = document.getElementById('vacationRequestsCount');

        if (travelCountElement && travelCountElement.textContent === '0') {
            travelCountElement.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
        }

        if (vacationCountElement && vacationCountElement.textContent === '0') {
            vacationCountElement.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
        }

        // محاولة جلب البيانات من الخادم باستخدام API Client
        try {
            // جلب البيانات من الخادم باستخدام API Client
            const [travelRequests, vacationRequests] = await Promise.all([
                apiClient.getTravelRequests(),
                apiClient.getVacationRequests()
            ]);

            const travelCount = travelRequests.length;
            const vacationCount = vacationRequests.length;

            console.log(`عدد طلبات السفر: ${travelCount}, عدد طلبات الإجازة: ${vacationCount}`);

            // تحديث عداد طلبات السفر
            if (travelCountElement) {
                travelCountElement.textContent = travelCount;
            }

            // تحديث عداد طلبات الإجازة
            if (vacationCountElement) {
                vacationCountElement.textContent = vacationCount;
            }

            // لا نستخدم التخزين المحلي بعد الآن

            return true;
        } catch (apiError) {
            console.error('خطأ في جلب البيانات من API:', apiError);
            throw apiError; // إعادة رمي الخطأ للمعالجة في الـ catch الخارجي
        }
    } catch (error) {
        console.error('خطأ في تحديث عدادات الطلبات:', error);

        // في حالة فشل الاتصال بالخادم، نعرض رسالة خطأ
        try {
            console.log('فشل في الاتصال بالخادم لتحديث العدادات');

            // تحديث عداد طلبات السفر ليظهر 0
            const travelCountElement = document.getElementById('travelRequestsCount');
            if (travelCountElement) {
                travelCountElement.textContent = '0';
            }

            // تحديث عداد طلبات الإجازة ليظهر 0
            const vacationCountElement = document.getElementById('vacationRequestsCount');
            if (vacationCountElement) {
                vacationCountElement.textContent = '0';
            }

            console.log('تم تحديث العدادات بقيم افتراضية');
            return true;
        } catch (localError) {
            console.error('خطأ في استخدام البيانات المحلية:', localError);

            // في حالة فشل كل المحاولات، عرض علامة الخطأ
            const travelCountElement = document.getElementById('travelRequestsCount');
            const vacationCountElement = document.getElementById('vacationRequestsCount');

            if (travelCountElement && travelCountElement.textContent === '0') {
                travelCountElement.textContent = '!';
                travelCountElement.title = 'فشل في جلب البيانات من قاعدة البيانات';
            }

            if (vacationCountElement && vacationCountElement.textContent === '0') {
                vacationCountElement.textContent = '!';
                vacationCountElement.title = 'فشل في جلب البيانات من قاعدة البيانات';
            }

            return false;
        }
    }
}

// دالة لتحديث جدول الطلبات في واجهة إدخال الطلبات
function updateRequestsEntryTable() {
    try {
        console.log('تحديث جدول الطلبات في واجهة إدخال الطلبات');

        const tableBody = document.querySelector('#requestsEntryTable tbody');
        if (!tableBody) {
            console.error('لم يتم العثور على جدول الطلبات في واجهة إدخال الطلبات');
            return;
        }

        // عرض رسالة تحميل
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل البيانات من الخادم...</p>
                </td>
            </tr>
        `;

        // جلب طلبات السفر من الخادم
        fetch('/api/travel-requests')
            .then(response => {
                if (!response.ok) {
                    throw new Error('فشل في جلب طلبات السفر من الخادم');
                }
                return response.json();
            })
            .then(travelData => {
                // جلب طلبات الإجازة من الخادم
                return fetch('/api/vacation-requests')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('فشل في جلب طلبات الإجازة من الخادم');
                        }
                        return response.json();
                    })
                    .then(vacationData => {
                        // استخراج البيانات من الاستجابة
                        const travelRequests = travelData.data || [];
                        const vacationRequests = vacationData.data || [];

                        console.log('عدد طلبات السفر من الخادم:', travelRequests.length);
                        console.log('عدد طلبات الإجازة من الخادم:', vacationRequests.length);

                        // تنسيق التواريخ
                        const formatDate = (dateString) => {
                            if (!dateString) return '-';
                            try {
                                return new Date(dateString).toLocaleDateString('ar-SA');
                            } catch (e) {
                                return dateString;
                            }
                        };

                        // تحويل طلبات السفر إلى التنسيق المطلوب
                        const formattedTravelRequests = travelRequests.map(request => ({
                            id: request.id,
                            type: 'travel',
                            managerName: request.manager_name || request.fullName,
                            position: request.position || request.manager_position,
                            department: request.department || request.manager_department,
                            createdAt: request.created_at || request.submissionDate,
                            startDate: request.departure_date || request.departureDate,
                            endDate: request.return_date || request.returnDate,
                            travelCountry: request.travel_country || request.travelCountry,
                            travelPurpose: request.travel_purpose || request.travelPurpose,
                            deputyName: request.deputy_name || request.deputyName,
                            deputyPosition: request.deputy_position || request.deputyPosition
                        }));

                        // تحويل طلبات الإجازة إلى التنسيق المطلوب
                        const formattedVacationRequests = vacationRequests.map(request => ({
                            id: request.id,
                            type: 'vacation',
                            managerName: request.manager_name || request.fullName,
                            position: request.position || request.manager_position,
                            department: request.department || request.manager_department,
                            createdAt: request.created_at || request.submissionDate,
                            startDate: request.start_date || request.startDate,
                            endDate: request.end_date || request.endDate,
                            vacationType: request.vacation_type || request.vacationType,
                            deputyName: request.deputy_name || request.deputyName,
                            deputyPosition: request.deputy_position || request.deputyPosition
                        }));

                        // دمج الطلبات
                        const allRequests = [...formattedTravelRequests, ...formattedVacationRequests];

                        // ترتيب الطلبات حسب التاريخ (الأحدث أولاً)
                        allRequests.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

                        if (allRequests.length > 0) {
                            // إنشاء صفوف الجدول
                            const rows = allRequests.map(request => {
                                // تحديد نوع الطلب ورمزه
                                const isVacation = request.type === 'vacation';
                                const icon = isVacation ? 'fa-calendar' : 'fa-plane';
                                const iconColor = isVacation ? 'success' : 'primary';
                                const requestTypeText = isVacation ? 'طلب إجازة' : 'طلب سفر';

                                // تحديد معلومات إضافية للعرض في الجدول
                                const additionalInfo = isVacation
                                    ? `<span class="badge bg-success">${getVacationTypeName(request.vacationType)}</span>`
                                    : `<span class="badge bg-info">${request.travelCountry || 'غير محدد'}</span>`;

                                return `
                                <tr>
                                    <td>#${request.id}</td>
                                    <td>
                                        <i class="fas ${icon} text-${iconColor} me-2"></i>
                                        ${requestTypeText}
                                        <br>
                                        <small>${additionalInfo}</small>
                                    </td>
                                    <td>${request.managerName || '-'}</td>
                                    <td>${request.position || '-'}</td>
                                    <td>${formatDate(request.createdAt)}</td>
                                    <td>${formatDate(request.startDate)}</td>
                                    <td>${formatDate(request.endDate)}</td>
                                    <td>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-info" title="عرض التفاصيل" onclick="showRequestDetails(${request.id}, '${request.type}')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-primary" title="تعديل" onclick="editRequest(${request.id}, '${request.type}')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" title="حذف" onclick="deleteRequest(${request.id}, '${request.type}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                `;
                            }).join('');

                            tableBody.innerHTML = rows;
                        } else {
                            tableBody.innerHTML = `
                                <tr>
                                    <td colspan="8" class="text-center">لا توجد طلبات مسجلة</td>
                                </tr>
                            `;
                        }
                    })
                    .catch(error => {
                        console.error('خطأ في جلب بيانات الطلبات من الخادم:', error);

                        // في حالة فشل الاتصال بالخادم، استخدم البيانات المحلية
                        const travelRequests = JSON.parse(localStorage.getItem('travelRequests') || '[]');
                        const vacationRequests = JSON.parse(localStorage.getItem('vacationRequests') || '[]');

                        console.log('استخدام البيانات المحلية كبديل. عدد طلبات السفر:', travelRequests.length);
                        console.log('استخدام البيانات المحلية كبديل. عدد طلبات الإجازة:', vacationRequests.length);

                        // تنسيق التواريخ
                        const formatDate = (dateString) => {
                            if (!dateString) return '-';
                            try {
                                return new Date(dateString).toLocaleDateString('ar-SA');
                            } catch (e) {
                                return dateString;
                            }
                        };

                        // تحويل طلبات السفر إلى التنسيق المطلوب
                        const formattedTravelRequests = travelRequests.map(request => ({
                            id: request.id,
                            type: 'travel',
                            managerName: request.fullName,
                            position: request.position,
                            department: request.department,
                            createdAt: request.submissionDate,
                            startDate: request.departureDate,
                            endDate: request.returnDate,
                            travelCountry: request.travelCountry,
                            travelPurpose: request.travelPurpose,
                            deputyName: request.deputyName,
                            deputyPosition: request.deputyPosition
                        }));

                        // تحويل طلبات الإجازة إلى التنسيق المطلوب
                        const formattedVacationRequests = vacationRequests.map(request => ({
                            id: request.id,
                            type: 'vacation',
                            managerName: request.fullName,
                            position: request.position,
                            department: request.department,
                            createdAt: request.submissionDate,
                            startDate: request.startDate,
                            endDate: request.endDate,
                            vacationType: request.vacationType,
                            deputyName: request.deputyName,
                            deputyPosition: request.deputyPosition
                        }));

                        // دمج الطلبات
                        const allRequests = [...formattedTravelRequests, ...formattedVacationRequests];

                        // ترتيب الطلبات حسب التاريخ (الأحدث أولاً)
                        allRequests.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

                        if (allRequests.length > 0) {
                            // إنشاء صفوف الجدول
                            const rows = allRequests.map(request => {
                                // تحديد نوع الطلب ورمزه
                                const isVacation = request.type === 'vacation' || request.requestType === 'vacation';
                                const icon = isVacation ? 'fa-calendar' : 'fa-plane';
                                const iconColor = isVacation ? 'success' : 'primary';
                                const requestTypeText = isVacation ? 'طلب إجازة' : 'طلب سفر';
                                const requestType = isVacation ? 'vacation' : 'travel';

                                // تحديد معلومات إضافية للعرض في الجدول
                                const additionalInfo = isVacation
                                    ? `<span class="badge bg-success">${getVacationTypeName(request.vacationType)}</span>`
                                    : `<span class="badge bg-info">${request.travelCountry || 'غير محدد'}</span>`;

                                return `
                                <tr>
                                    <td>#${request.id}</td>
                                    <td>
                                        <i class="fas ${icon} text-${iconColor} me-2"></i>
                                        ${requestTypeText}
                                        <br>
                                        <small>${additionalInfo}</small>
                                    </td>
                                    <td>${request.managerName || '-'}</td>
                                    <td>${request.position || '-'}</td>
                                    <td>${formatDate(request.createdAt)}</td>
                                    <td>${formatDate(request.startDate)}</td>
                                    <td>${formatDate(request.endDate)}</td>
                                    <td>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-info" title="عرض التفاصيل" onclick="showRequestDetails(${request.id}, '${requestType}')">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-primary" title="تعديل" onclick="editRequest(${request.id}, '${requestType}')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" title="حذف" onclick="deleteRequest(${request.id}, '${requestType}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                `;
                            }).join('');

                            tableBody.innerHTML = rows;
                        } else {
                            tableBody.innerHTML = `
                                <tr>
                                    <td colspan="8" class="text-center">لا توجد
            // عرض رسالة عدم وجود بيانات
            container.innerHTML = `
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد بيانات متاحة للمدراء الأكثر سفراً
                </div>
            `;

            // إنشاء رسم بياني فارغ
            createTravelChart([], [], []);
        }

        console.log('تم تحديث بيانات المدراء الأكثر سفراً بنجاح');
    } catch (error) {
        console.error('خطأ في تحديث بيانات المدراء الأكثر سفراً من الخادم:', error);

        // عرض رسالة الخطأ
        if (container) {
            container.innerHTML = `
                <div class="alert alert-danger text-center">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>حدث خطأ أثناء جلب بيانات المدراء الأكثر سفراً من قاعدة البيانات</strong>
                    <hr>
                    <p>${error.message}</p>
                    <button class="btn btn-primary btn-sm" onclick="updateMostTravelingManagersTable()">
                        <i class="fas fa-sync-alt me-1"></i>
                        إعادة المحاولة
                    </button>
                </div>
            `;
        }

        // إنشاء رسم بياني فارغ
        createTravelChart([], [], []);

        // عرض رسالة خطأ للمستخدم
        showToast('خطأ في جلب البيانات', 'فشل في جلب بيانات المدراء الأكثر سفراً من قاعدة البيانات. يرجى التحقق من اتصالك بالخادم وإعادة المحاولة.', 'error');
    }
}

// دالة لعرض بيانات المدراء الأكثر سفراً
function displayTravelingManagersData(container, managersData) {
    try {
        console.log('عرض بيانات المدراء الأكثر سفراً:', managersData);

        // تفريغ الحاوية
        container.innerHTML = '';

        // إنشاء قائمة المدراء الأكثر سفراً
        const managersList = document.createElement('div');
        managersList.className = 'list-group';

        // إنشاء مصفوفات للرسم البياني
        const labels = [];
        const counts = [];
        const backgroundColors = [
            'rgba(54, 162, 235, 0.8)',
            'rgba(75, 192, 192, 0.8)',
            'rgba(153, 102, 255, 0.8)',
            'rgba(255, 159, 64, 0.8)',
            'rgba(255, 99, 132, 0.8)'
        ];

        // إضافة بطاقات للمدراء الأكثر سفراً
        managersData.forEach((manager, index) => {
            // استخراج البيانات مع مراعاة الاختلافات في هيكل البيانات بين API والتخزين المحلي
            const managerName = manager.manager_name || manager.fullName || 'غير محدد';
            const managerPosition = manager.manager_position || manager.position || 'غير محدد';
            const managerDepartment = manager.manager_department || manager.department || 'غير محدد';

            // استخراج عدد مرات السفر
            const travelCount = manager.travel_count || 0;

            // تنسيق تاريخ آخر سفر
            let formattedLastTravelDate = 'غير محدد';
            if (manager.last_travel_date) {
                const lastTravelDate = new Date(manager.last_travel_date);
                if (!isNaN(lastTravelDate.getTime())) {
                    formattedLastTravelDate = lastTravelDate.toLocaleDateString('ar-SA');
                }
            }

            // إضافة عنصر للقائمة
            const listItem = document.createElement('div');
            listItem.className = 'list-group-item list-group-item-action d-flex justify-content-between align-items-center';

            listItem.innerHTML = `
                <div>
                    <div class="d-flex align-items-center">
                        <span class="badge bg-primary rounded-circle me-2">${index + 1}</span>
                        <h6 class="mb-0">${managerName}</h6>
                    </div>
                    <small class="text-muted">${managerPosition} - ${managerDepartment}</small>
                </div>
                <div class="text-end">
                    <h5><span class="badge bg-primary">${travelCount} سفرة</span></h5>
                    <small class="text-muted">آخر سفر: ${formattedLastTravelDate}</small>
                </div>
            `;

            managersList.appendChild(listItem);

            // إضافة البيانات للرسم البياني
            labels.push(managerName);
            counts.push(travelCount);
        });

        container.appendChild(managersList);

        // إنشاء الرسم البياني
        createTravelChart(labels, counts, backgroundColors);

        console.log('تم عرض بيانات المدراء الأكثر سفراً بنجاح');
    } catch (error) {
        console.error('خطأ في عرض بيانات المدراء الأكثر سفراً:', error);
        container.innerHTML = `
            <div class="alert alert-danger text-center">
                <i class="fas fa-exclamation-triangle me-2"></i>
                حدث خطأ أثناء عرض البيانات: ${error.message}
            </div>
        `;

        // إنشاء رسم بياني فارغ
        createTravelChart([], [], []);
    }
}

// دالة لإنشاء الرسم البياني للمدراء الأكثر سفراً
function createTravelChart(labels, data, backgroundColors) {
    try {
        const ctx = document.getElementById('travelChart').getContext('2d');

        // إذا كان الرسم البياني موجوداً بالفعل، قم بتدميره
        if (travelChart) {
            travelChart.destroy();
        }

        // إنشاء رسم بياني جديد
        travelChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: backgroundColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: {
                                family: 'Tajawal, sans-serif'
                            }
                        }
                    },
                    title: {
                        display: true,
                        text: 'توزيع عدد مرات السفر',
                        font: {
                            family: 'Tajawal, sans-serif',
                            size: 16
                        }
                    }
                }
            }
        });
    } catch (error) {
        console.error('خطأ في إنشاء الرسم البياني للمدراء الأكثر سفراً:', error);
    }
}

// دالة لتحديث بيانات المدراء الأكثر إجازةً
async function updateMostVacationingManagersTable() {
    try {
        console.log('تحديث بيانات المدراء الأكثر إجازةً من قاعدة البيانات');
        const container = document.getElementById('mostVacationingManagersList');
        if (!container) {
            console.error('لم يتم العثور على حاوية المدراء الأكثر إجازةً');
            return;
        }

        // عرض رسالة التحميل
        container.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-success" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل بيانات المدراء الأكثر إجازةً من قاعدة البيانات...</p>
            </div>
        `;

        // إضافة تسجيل إضافي للتشخيص
        console.log('قبل استدعاء apiClient.getMostVacationingManagers');

        // جلب بيانات المدراء الأكثر إجازةً من قاعدة البيانات
        const topManagers = await apiClient.getMostVacationingManagers(5);

        console.log('عدد المدراء الأكثر إجازةً من قاعدة البيانات:', topManagers.length);
        console.log('بيانات المدراء الأكثر إجازةً من قاعدة البيانات:', JSON.stringify(topManagers));

        if (topManagers.length > 0) {
            // عرض بيانات المدراء الأكثر إجازةً
            displayVacationingManagersData(container, topManagers);
        } else {
            // عرض رسالة عدم وجود بيانات
            container.innerHTML = `
                <div class="alert alert-warning text-center">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    لا توجد بيانات متاحة للمدراء الأكثر إجازةً في قاعدة البيانات
                    <hr>
                    <p>يمكنك إضافة بيانات تجريبية باستخدام الزر أدناه:</p>
                    <button id="seedVacationDataBtn" class="btn btn-success">
                        <i class="fas fa-database me-1"></i>
                        إضافة بيانات تجريبية
                    </button>
                </div>
            `;

            // إضافة حدث النقر على زر إضافة البيانات التجريبية
            const seedButton = document.getElementById('seedVacationDataBtn');
            if (seedButton) {
                seedButton.addEventListener('click', async () => {
                    try {
                        seedButton.disabled = true;
                        seedButton.innerHTML = `
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            جاري إضافة البيانات...
                        `;

                        // استدعاء نقطة النهاية لإضافة بيانات تجريبية
                        const response = await fetch('/api/seed-vacation-data', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });

                        const result = await response.json();

                        if (response.ok) {
                            alert(`تم إضافة البيانات التجريبية بنجاح: ${result.message}`);
                            // إعادة تحميل البيانات
                            updateMostVacationingManagersTable();
                        } else {
                            alert(`فشل في إضافة البيانات التجريبية: ${result.message || 'خطأ غير معروف'}`);
                            seedButton.disabled = false;
                            seedButton.innerHTML = `
                                <i class="fas fa-database me-1"></i>
                                إضافة بيانات تجريبية
                            `;
                        }
                    } catch (error) {
                        console.error('خطأ في إضافة البيانات التجريبية:', error);
                        alert('حدث خطأ أثناء إضافة البيانات التجريبية. يرجى المحاولة مرة أخرى.');
                        seedButton.disabled = false;
                        seedButton.innerHTML = `
                            <i class="fas fa-database me-1"></i>
                            إضافة بيانات تجريبية
                        `;
                    }
                });
            }

            // إنشاء رسم بياني فارغ
            createVacationChart([], [], []);
        }

        console.log('تم تحديث بيانات المدراء الأكثر إجازةً بنجاح');
    } catch (error) {
        console.error('خطأ في تحديث بيانات المدراء الأكثر إجازةً من قاعدة البيانات:', error);

        // عرض رسالة الخطأ
        if (container) {
            container.innerHTML = `
                <div class="alert alert-danger text-center">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>حدث خطأ أثناء جلب البيانات من قاعدة البيانات</strong>
                    <hr>
                    <p>${error.message}</p>
                    <button id="retryVacationDataBtn" class="btn btn-primary">
                        <i class="fas fa-sync-alt me-1"></i>
                        إعادة المحاولة
                    </button>
                </div>
            `;

            // إضافة حدث النقر على زر إعادة المحاولة
            const retryButton = document.getElementById('retryVacationDataBtn');
            if (retryButton) {
                retryButton.addEventListener('click', () => {
                    updateMostVacationingManagersTable();
                });
            }
        }

        // إنشاء رسم بياني فارغ
        createVacationChart([], [], []);

        // عرض رسالة خطأ للمستخدم
        showToast('خطأ في جلب البيانات', 'فشل في جلب بيانات المدراء الأكثر إجازةً من قاعدة البيانات. يرجى التحقق من اتصالك بالخادم وإعادة المحاولة.', 'error');
    }
}

// دالة لعرض بيانات المدراء الأكثر إجازةً
function displayVacationingManagersData(container, managersData) {
    try {
        console.log('عرض بيانات المدراء الأكثر إجازةً:', managersData);

        // تسجيل إضافي للتشخيص
        console.log('نوع البيانات المستلمة:', typeof managersData);
        console.log('هل البيانات مصفوفة؟', Array.isArray(managersData));

        if (!Array.isArray(managersData)) {
            console.error('البيانات المستلمة ليست مصفوفة!');
            container.innerHTML = `
                <div class="alert alert-danger text-center">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    خطأ في تنسيق البيانات: البيانات المستلمة ليست مصفوفة
                </div>
            `;
            return;
        }

        if (managersData.length === 0) {
            console.warn('مصفوفة البيانات فارغة!');
            container.innerHTML = `
                <div class="alert alert-warning text-center">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد بيانات للمدراء الأكثر إجازة
                </div>
            `;
            return;
        }

        // تفريغ الحاوية
        container.innerHTML = '';

        // إنشاء قائمة المدراء الأكثر إجازةً
        const managersList = document.createElement('div');
        managersList.className = 'list-group';

        // إنشاء مصفوفات للرسم البياني
        const labels = [];
        const counts = [];
        const backgroundColors = [
            'rgba(40, 167, 69, 0.8)',
            'rgba(23, 162, 184, 0.8)',
            'rgba(255, 193, 7, 0.8)',
            'rgba(220, 53, 69, 0.8)',
            'rgba(108, 117, 125, 0.8)'
        ];

        // إضافة بطاقات للمدراء الأكثر إجازةً
        managersData.forEach((manager, index) => {
            // تسجيل بيانات كل مدير للتشخيص
            console.log(`بيانات المدير ${index + 1}:`, manager);

            // استخراج البيانات من قاعدة البيانات فقط
            const managerName = manager.manager_name || 'غير محدد';
            const managerPosition = manager.manager_position || 'غير محدد';
            const managerDepartment = manager.manager_department || 'غير محدد';

            // استخراج عدد الإجازات وعدد الأيام
            const vacationCount = manager.vacation_count || 0;
            const vacationDaysTotal = manager.vacation_days_total || 0;

            // تنسيق تاريخ آخر إجازة
            let formattedLastVacationDate = 'غير محدد';
            if (manager.last_vacation_date) {
                const lastVacationDate = new Date(manager.last_vacation_date);
                if (!isNaN(lastVacationDate.getTime())) {
                    formattedLastVacationDate = lastVacationDate.toLocaleDateString('ar-SA');
                }
            }

            // إضافة عنصر للقائمة
            const listItem = document.createElement('div');
            listItem.className = 'list-group-item list-group-item-action d-flex justify-content-between align-items-center';

            listItem.innerHTML = `
                <div>
                    <div class="d-flex align-items-center">
                        <span class="badge bg-success rounded-circle me-2">${index + 1}</span>
                        <h6 class="mb-0">${managerName}</h6>
                    </div>
                    <small class="text-muted">${managerPosition} - ${managerDepartment}</small>
                </div>
                <div class="text-end">
                    <div>
                        <span class="badge bg-success me-1">${vacationCount} إجازة</span>
                        <span class="badge bg-info">${vacationDaysTotal} يوم</span>
                    </div>
                    <small class="text-muted">آخر إجازة: ${formattedLastVacationDate}</small>
                </div>
            `;

            managersList.appendChild(listItem);

            // إضافة البيانات للرسم البياني
            labels.push(managerName);
            counts.push(vacationDaysTotal);
        });

        container.appendChild(managersList);

        // إنشاء الرسم البياني
        createVacationChart(labels, counts, backgroundColors);

        console.log('تم عرض بيانات المدراء الأكثر إجازةً بنجاح');
    } catch (error) {
        console.error('خطأ في عرض بيانات المدراء الأكثر إجازةً:', error);
        container.innerHTML = `
            <div class="alert alert-danger text-center">
                <i class="fas fa-exclamation-triangle me-2"></i>
                حدث خطأ أثناء عرض البيانات: ${error.message}
            </div>
        `;

        // إنشاء رسم بياني فارغ
        createVacationChart([], [], []);
    }
}

// دالة لإنشاء الرسم البياني للمدراء الأكثر إجازةً
function createVacationChart(labels, data, backgroundColors) {
    try {
        const ctx = document.getElementById('vacationChart').getContext('2d');

        // إذا كان الرسم البياني موجوداً بالفعل، قم بتدميره
        if (vacationChart) {
            vacationChart.destroy();
        }

        // إنشاء رسم بياني جديد
        vacationChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: backgroundColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: {
                                family: 'Tajawal, sans-serif'
                            }
                        }
                    },
                    title: {
                        display: true,
                        text: 'توزيع أيام الإجازات',
                        font: {
                            family: 'Tajawal, sans-serif',
                            size: 16
                        }
                    }
                }
            }
        });
    } catch (error) {
        console.error('خطأ في إنشاء الرسم البياني للمدراء الأكثر إجازةً:', error);
    }
}

// دالة لتحديث تبويب المدراء المسافرين
async function updateTravelingTab() {
    try {
        console.log('تحديث جدول المدراء المسافرين من قاعدة البيانات');
        const tableBody = document.getElementById('travelingManagersTable');
        if (!tableBody) {
            console.error('لم يتم العثور على جدول المدراء المسافرين');
            return;
        }

        // عرض رسالة التحميل
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل بيانات المدراء المسافرين من قاعدة البيانات...</p>
                </td>
            </tr>
        `;

        // محاولة جلب البيانات من قاعدة البيانات
        let currentlyTraveling = [];
        let fromLocalStorage = false;

        try {
            // جلب بيانات المدراء المسافرين حالياً من الخادم
            console.log('جلب بيانات المدراء المسافرين من قاعدة البيانات...');
            currentlyTraveling = await apiClient.getCurrentlyTravelingManagers();

            // لا نستخدم التخزين المحلي بعد الآن
            console.log('تم جلب بيانات المدراء المسافرين بنجاح');

            console.log('عدد المدراء المسافرين حالياً من قاعدة البيانات:', currentlyTraveling.length);
        } catch (apiError) {
            console.error('خطأ في جلب بيانات المدراء المسافرين من قاعدة البيانات:', apiError);

            // لا نستخدم التخزين المحلي بعد الآن
            console.log('فشل في جلب بيانات المدراء المسافرين من قاعدة البيانات');
            // استخدام مصفوفة فارغة
            currentlyTraveling = [];
            throw apiError; // إعادة رمي الخطأ الأصلي
        }

        // محاولة استخدام بيانات طلبات السفر الحالية إذا لم تكن هناك بيانات للمدراء المسافرين
        if (currentlyTraveling.length === 0 && !fromLocalStorage) {
            console.log('محاولة استخراج المدراء المسافرين من طلبات السفر...');

            try {
                // جلب جميع طلبات السفر
                const travelRequests = await apiClient.getTravelRequests();

                // فلترة الطلبات للحصول على المدراء المسافرين حالياً
                const today = new Date();
                today.setHours(0, 0, 0, 0);

                currentlyTraveling = travelRequests.filter(request => {
                    const departureDate = new Date(request.departure_date || request.departureDate);
                    const returnDate = new Date(request.return_date || request.returnDate);

                    departureDate.setHours(0, 0, 0, 0);
                    returnDate.setHours(0, 0, 0, 0);

                    return departureDate <= today && returnDate >= today;
                });

                console.log('تم استخراج المدراء المسافرين من طلبات السفر. عدد المدراء:', currentlyTraveling.length);

                // لا نستخدم التخزين المحلي بعد الآن
            } catch (requestsError) {
                console.error('خطأ في استخراج المدراء المسافرين من طلبات السفر:', requestsError);
            }
        }

        console.log('بيانات المدراء المسافرين:', JSON.stringify(currentlyTraveling));

        if (currentlyTraveling.length > 0) {
            // تفريغ الجدول
            tableBody.innerHTML = '';

            // إضافة صفوف للمدراء المسافرين
            currentlyTraveling.forEach(manager => {
                const row = document.createElement('tr');

                // تنسيق تاريخ العودة
                let returnDate;
                try {
                    returnDate = new Date(manager.return_date || manager.returnDate).toLocaleDateString('ar-SA');
                } catch (e) {
                    console.error('خطأ في تنسيق تاريخ العودة:', e);
                    returnDate = 'تاريخ غير صالح';
                }

                row.innerHTML = `
                    <td>${manager.manager_name || manager.fullName || 'غير محدد'}</td>
                    <td>${manager.manager_position || manager.position || 'غير محدد'}</td>
                    <td>${manager.manager_department || manager.department || 'غير محدد'}</td>
                    <td>${manager.travel_country || manager.travelCountry || 'غير محدد'}</td>
                    <td>${returnDate}</td>
                    <td>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-primary me-2">${manager.deputy_name || manager.deputyName || 'غير محدد'}</span>
                            <small>${manager.deputy_position || manager.deputyPosition || 'غير محدد'}</small>
                        </div>
                    </td>
                    <td>${manager.contact_numbers || manager.deputy_phone || manager.deputyPhone || 'غير متوفر'}</td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-danger" onclick="cancelTravel(${manager.id || manager.requestId})" title="إلغاء السفر">
                                <i class="fas fa-times-circle"></i>
                            </button>
                            <button type="button" class="btn btn-warning" onclick="extendTravel(${manager.id || manager.requestId})" title="تمديد السفر">
                                <i class="fas fa-calendar-plus"></i>
                            </button>
                        </div>
                    </td>
                `;
                tableBody.appendChild(row);
            });

            // تحديث عداد المدراء المسافرين في الصفحة الرئيسية
            const travelingCountElement = document.getElementById('travelingManagersCount');
            if (travelingCountElement) {
                travelingCountElement.textContent = currentlyTraveling.length;
            }

            // إضافة إشارة إذا كانت البيانات من التخزين المحلي
            if (fromLocalStorage) {
                const infoRow = document.createElement('tr');
                infoRow.innerHTML = `
                    <td colspan="8" class="text-center">
                        <div class="alert alert-warning mb-0 py-1">
                            <small><i class="fas fa-info-circle me-1"></i> تم عرض البيانات من التخزين المحلي. قد لا تكون محدثة.</small>
                        </div>
                    </td>
                `;
                tableBody.appendChild(infoRow);
            }
        } else {
            // عرض رسالة عدم وجود بيانات
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center">
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            لا يوجد مدراء مسافرين حالياً
                        </div>
                    </td>
                </tr>
            `;

            // تصفير عداد المدراء المسافرين في الصفحة الرئيسية
            const travelingCountElement = document.getElementById('travelingManagersCount');
            if (travelingCountElement) {
                travelingCountElement.textContent = '0';
            }
        }

        console.log('تم تحديث جدول المدراء المسافرين بنجاح');
        return true;
    } catch (error) {
        console.error('خطأ في تحديث جدول المدراء المسافرين:', error);

        // عرض رسالة الخطأ في الجدول
        const tableBody = document.getElementById('travelingManagersTable');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center">
                        <div class="alert alert-danger mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>حدث خطأ أثناء جلب بيانات المدراء المسافرين من قاعدة البيانات</strong>
                            <hr>
                            <p>${error.message}</p>
                            <button class="btn btn-primary btn-sm" onclick="updateTravelingTab()">
                                <i class="fas fa-sync-alt me-1"></i>
                                إعادة المحاولة
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }

        // تصفير عداد المدراء المسافرين في الصفحة الرئيسية
        const travelingCountElement = document.getElementById('travelingManagersCount');
        if (travelingCountElement) {
            travelingCountElement.textContent = '!';
            travelingCountElement.title = 'فشل في جلب البيانات من قاعدة البيانات';
        }

        // عرض رسالة خطأ للمستخدم
        showToast('خطأ في جلب البيانات', 'فشل في جلب بيانات المدراء المسافرين من قاعدة البيانات. يرجى التحقق من اتصالك بالخادم وإعادة المحاولة.', 'error');
        return false;
    }
}

// دالة لتحديث تبويب المدراء في إجازة
async function updateVacationingTab() {
    try {
        console.log('تحديث جدول المدراء في إجازة من قاعدة البيانات');
        const tableBody = document.getElementById('vacationingManagersTable');
        if (!tableBody) {
            console.error('لم يتم العثور على جدول المدراء في إجازة');
            return;
        }

        // عرض رسالة التحميل
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل بيانات المدراء في إجازة من قاعدة البيانات...</p>
                </td>
            </tr>
        `;

        // محاولة جلب البيانات من قاعدة البيانات
        let currentlyVacationing = [];
        let fromLocalStorage = false;

        try {
            // جلب بيانات المدراء في إجازة حالياً من الخادم
            console.log('جلب بيانات المدراء في إجازة من قاعدة البيانات...');
            currentlyVacationing = await apiClient.getCurrentlyVacationingManagers();

            // لا نستخدم التخزين المحلي بعد الآن
            console.log('تم جلب بيانات المدراء في إجازة بنجاح');

            console.log('عدد المدراء في إجازة حالياً من قاعدة البيانات:', currentlyVacationing.length);
        } catch (apiError) {
            console.error('خطأ في جلب بيانات المدراء في إجازة من قاعدة البيانات:', apiError);

            // لا نستخدم التخزين المحلي بعد الآن
            console.log('فشل في جلب بيانات المدراء في إجازة من قاعدة البيانات');
            // استخدام مصفوفة فارغة
            currentlyVacationing = [];
            throw apiError; // إعادة رمي الخطأ الأصلي
        }

        // محاولة استخدام بيانات طلبات الإجازة الحالية إذا لم تكن هناك بيانات للمدراء في إجازة
        if (currentlyVacationing.length === 0 && !fromLocalStorage) {
            console.log('محاولة استخراج المدراء في إجازة من طلبات الإجازة...');

            try {
                // جلب جميع طلبات الإجازة
                const vacationRequests = await apiClient.getVacationRequests();

                // فلترة الطلبات للحصول على المدراء في إجازة حالياً
                const today = new Date();
                today.setHours(0, 0, 0, 0);

                currentlyVacationing = vacationRequests.filter(request => {
                    const startDate = new Date(request.start_date || request.startDate);
                    const endDate = new Date(request.end_date || request.endDate);

                    startDate.setHours(0, 0, 0, 0);
                    endDate.setHours(0, 0, 0, 0);

                    return startDate <= today && endDate >= today;
                });

                console.log('تم استخراج المدراء في إجازة من طلبات الإجازة. عدد المدراء:', currentlyVacationing.length);

                // لا نستخدم التخزين المحلي بعد الآن
            } catch (requestsError) {
                console.error('خطأ في استخراج المدراء في إجازة من طلبات الإجازة:', requestsError);
            }
        }

        console.log('بيانات المدراء في إجازة:', JSON.stringify(currentlyVacationing));

        if (currentlyVacationing.length > 0) {
            // تفريغ الجدول
            tableBody.innerHTML = '';

            // إضافة صفوف للمدراء في إجازة
            currentlyVacationing.forEach(manager => {
                const row = document.createElement('tr');

                // تنسيق تاريخ نهاية الإجازة
                let endDate;
                try {
                    endDate = new Date(manager.end_date || manager.endDate).toLocaleDateString('ar-SA');
                } catch (e) {
                    console.error('خطأ في تنسيق تاريخ نهاية الإجازة:', e);
                    endDate = 'تاريخ غير صالح';
                }

                // ترجمة نوع الإجازة
                let vacationType = 'غير محدد';
                const vacationTypeValue = manager.vacation_type || manager.vacationType;
                switch (vacationTypeValue) {
                    case 'annual':
                        vacationType = 'سنوية';
                        break;
                    case 'sick':
                        vacationType = 'مرضية';
                        break;
                    case 'emergency':
                        vacationType = 'اضطرارية';
                        break;
                    case 'exceptional':
                        vacationType = 'استثنائية';
                        break;
                    default:
                        vacationType = vacationTypeValue || 'غير محدد';
                }

                row.innerHTML = `
                    <td>${manager.manager_name || manager.fullName || 'غير محدد'}</td>
                    <td>${manager.manager_position || manager.position || 'غير محدد'}</td>
                    <td>${manager.manager_department || manager.department || 'غير محدد'}</td>
                    <td><span class="badge bg-success">${vacationType}</span></td>
                    <td>${endDate}</td>
                    <td>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-primary me-2">${manager.deputy_name || manager.deputyName || 'غير محدد'}</span>
                            <small>${manager.deputy_position || manager.deputyPosition || 'غير محدد'}</small>
                        </div>
                    </td>
                    <td>${manager.contact_numbers || manager.deputy_phone || manager.deputyPhone || 'غير متوفر'}</td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-danger" onclick="cancelVacation(${manager.id || manager.requestId})" title="إلغاء الإجازة">
                                <i class="fas fa-times-circle"></i>
                            </button>
                            <button type="button" class="btn btn-warning" onclick="extendVacation(${manager.id || manager.requestId})" title="تمديد الإجازة">
                                <i class="fas fa-calendar-plus"></i>
                            </button>
                        </div>
                    </td>
                `;
                tableBody.appendChild(row);
            });

            // تحديث عداد المدراء في إجازة في الصفحة الرئيسية
            const vacationingCountElement = document.getElementById('vacationingManagersCount');
            if (vacationingCountElement) {
                vacationingCountElement.textContent = currentlyVacationing.length;
            }

            // إضافة إشارة إذا كانت البيانات من التخزين المحلي
            if (fromLocalStorage) {
                const infoRow = document.createElement('tr');
                infoRow.innerHTML = `
                    <td colspan="8" class="text-center">
                        <div class="alert alert-warning mb-0 py-1">
                            <small><i class="fas fa-info-circle me-1"></i> تم عرض البيانات من التخزين المحلي. قد لا تكون محدثة.</small>
                        </div>
                    </td>
                `;
                tableBody.appendChild(infoRow);
            }
        } else {
            // عرض رسالة عدم وجود بيانات
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center">
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            لا يوجد مدراء في إجازة حالياً
                        </div>
                    </td>
                </tr>
            `;

            // تصفير عداد المدراء في إجازة في الصفحة الرئيسية
            const vacationingCountElement = document.getElementById('vacationingManagersCount');
            if (vacationingCountElement) {
                vacationingCountElement.textContent = '0';
            }
        }

        console.log('تم تحديث جدول المدراء في إجازة بنجاح');
        return true;
    } catch (error) {
        console.error('خطأ في تحديث جدول المدراء في إجازة:', error);

        // عرض رسالة الخطأ في الجدول
        const tableBody = document.getElementById('vacationingManagersTable');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center">
                        <div class="alert alert-danger mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>حدث خطأ أثناء جلب بيانات المدراء في إجازة من قاعدة البيانات</strong>
                            <hr>
                            <p>${error.message}</p>
                            <button class="btn btn-primary btn-sm" onclick="updateVacationingTab()">
                                <i class="fas fa-sync-alt me-1"></i>
                                إعادة المحاولة
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }

        // تصفير عداد المدراء في إجازة في الصفحة الرئيسية
        const vacationingCountElement = document.getElementById('vacationingManagersCount');
        if (vacationingCountElement) {
            vacationingCountElement.textContent = '!';
            vacationingCountElement.title = 'فشل في جلب البيانات من قاعدة البيانات';
        }

        // عرض رسالة خطأ للمستخدم
        showToast('خطأ في جلب البيانات', 'فشل في جلب بيانات المدراء في إجازة من قاعدة البيانات. يرجى التحقق من اتصالك بالخادم وإعادة المحاولة.', 'error');
        return false;
    }
}

// دالة لإغلاق النافذة المنبثقة يدوياً
function closeRequestDetailsModal() {
    try {
        const modalElement = document.getElementById('requestDetailsModal');
        if (!modalElement) return;
        
        if (typeof bootstrap !== 'undefined' && typeof bootstrap.Modal !== 'undefined') {
            // إغلاق باستخدام Bootstrap
            const modalInstance = bootstrap.Modal.getInstance(modalElement);
            if (modalInstance) {
                modalInstance.hide();
            }
        } else {
            // إغلاق يدوي
            modalElement.classList.remove('show');
            modalElement.style.display = 'none';
            document.body.classList.remove('modal-open');
            
            // إزالة الخلفية الداكنة
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }
        }
    } catch (error) {
        console.error('خطأ في إغلاق النافذة المنبثقة:', error);
    }
}

// دالة لعرض تفاصيل الطلب
function showRequestDetails(requestId, requestType) {
    try {
        console.log(`عرض تفاصيل الطلب رقم ${requestId} من نوع ${requestType}`);

        // عرض نافذة تفاصيل الطلب
        const modalElement = document.getElementById('requestDetailsModal');
        if (!modalElement) {
            console.error('لم يتم العثور على عنصر النافذة المنبثقة لتفاصيل الطلب');
            showAlert('لم يتم العثور على نافذة تفاصيل الطلب', 'danger');
            return;
        }
        
        // تأكد من تهيئة Bootstrap بشكل صحيح
        if (typeof bootstrap === 'undefined' || typeof bootstrap.Modal === 'undefined') {
            console.error('مكتبة Bootstrap غير متوفرة');
            // استخدام طريقة بديلة لعرض النافذة المنبثقة
            modalElement.classList.add('show');
            modalElement.style.display = 'block';
            document.body.classList.add('modal-open');
            
            // إضافة خلفية داكنة
            const backdrop = document.createElement('div');
            backdrop.className = 'modal-backdrop fade show';
            document.body.appendChild(backdrop);
            
            // إضافة مستمع أحداث لزر الإغلاق
            const closeButtons = modalElement.querySelectorAll('[data-bs-dismiss="modal"]');
            closeButtons.forEach(button => {
                button.addEventListener('click', closeRequestDetailsModal);
            });
        } else {
            // استخدام Bootstrap Modal
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        }

        // عرض مؤشر التحميل
        const contentElement = document.getElementById('requestDetailsContent');
        if (!contentElement) {
            console.error('لم يتم العثور على عنصر محتوى تفاصيل الطلب');
            return;
        }
        contentElement.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-3">جاري تحميل تفاصيل الطلب...</p>
            </div>
        `;

        // محاولة جلب البيانات من الخادم أولاً
        const apiEndpoint = requestType === 'travel' ?
            '/api/travel-requests/' + requestId :
            '/api/vacation-requests/' + requestId;

        console.log(`محاولة جلب البيانات من الخادم: ${apiEndpoint}`);

        // جلب تفاصيل الطلب المحدد من الخادم
        fetch(apiEndpoint)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`فشل في جلب البيانات من الخادم: ${response.status} ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('تم جلب البيانات من الخادم:', data);

                // التحقق من تنسيق البيانات
                let request;
                if (data.status === 'success' && data.data) {
                    request = data.data;
                } else if (data.id) {
                    request = data;
                } else {
                    throw new Error('تنسيق البيانات المستلمة غير متوقع');
                }

                // عرض تفاصيل الطلب
                displayRequestDetails(request, requestType);
            })
            .catch(serverError => {
                console.error('خطأ في جلب البيانات من الخادم:', serverError);

                // في حالة فشل الاتصال بالخادم، نحاول استخدام البيانات المحلية
                console.log('محاولة استخدام البيانات المحلية كبديل');

                // جلب بيانات الطلب من localStorage
                let request;
                if (requestType === 'travel') {
                    const travelRequests = JSON.parse(localStorage.getItem('travelRequests') || '[]');
                    request = travelRequests.find(req => req.id == requestId);
                } else {
                    const vacationRequests = JSON.parse(localStorage.getItem('vacationRequests') || '[]');
                    request = vacationRequests.find(req => req.id == requestId);
                }

                if (!request) {
                    document.getElementById('requestDetailsContent').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            لم يتم العثور على بيانات الطلب
                        </div>
                    `;
                    return;
                }

                // عرض تفاصيل الطلب من البيانات المحلية
                displayRequestDetails(request, requestType);
            });

    } catch (err) {
        console.error('خطأ في عرض تفاصيل الطلب:', err);
        document.getElementById('requestDetailsContent').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                حدث خطأ في عرض تفاصيل الطلب: ${err.message}
            </div>
        `;
    }
}

// دالة مساعدة لعرض تفاصيل الطلب
function displayRequestDetails(request, requestType) {
    console.log('عرض تفاصيل الطلب:', request);

    // تنسيق التواريخ
    const formatDate = (dateString) => {
        if (!dateString) return '-';
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-EG', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        } catch (e) {
            return dateString;
        }
    };

    // عرض تفاصيل الطلب
    let detailsHTML = '';

    // بيانات مقدم الطلب
    detailsHTML += `
        <div class="card mb-3">
            <div class="card-header bg-light">
                <h5 class="mb-0">بيانات مقدم الطلب</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-2">
                        <strong>الاسم الكامل:</strong> ${request.fullName || request.manager_name || '-'}
                    </div>
                    <div class="col-md-6 mb-2">
                        <strong>المنصب:</strong> ${request.position || request.manager_position || '-'}
                    </div>
                    <div class="col-md-6 mb-2">
                        <strong>القسم/الإدارة:</strong> ${request.department || request.manager_department || '-'}
                    </div>
                    <div class="col-md-6 mb-2">
                        <strong>تاريخ تقديم الطلب:</strong> ${formatDate(request.submissionDate || request.created_at)}
                    </div>
                </div>
            </div>
        </div>
    `;

    // تفاصيل الطلب
    if (requestType === 'travel') {
        detailsHTML += `
            <div class="card mb-3">
                <div class="card-header bg-light">
                    <h5 class="mb-0">تفاصيل السفر</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <strong>دولة السفر:</strong> ${request.travelCountry || request.travel_country || '-'}
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>نوع السفر:</strong> ${request.travelType || request.travel_type || '-'}
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>تاريخ الإخطار:</strong> ${formatDate(request.notificationDate || request.notification_date)}
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>تاريخ المغادرة:</strong> ${formatDate(request.departureDate || request.departure_date)}
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>تاريخ العودة:</strong> ${formatDate(request.returnDate || request.return_date)}
                        </div>
                        <div class="col-12 mb-2">
                            <strong>الغرض من السفر:</strong>
                            <p class="mt-1">${request.travelPurpose || request.travel_purpose || '-'}</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    } else {
        detailsHTML += `
            <div class="card mb-3">
                <div class="card-header bg-light">
                    <h5 class="mb-0">تفاصيل الإجازة</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <strong>نوع الإجازة:</strong> ${getVacationTypeName(request.vacationType || request.vacation_type) || '-'}
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>مدة الإجازة:</strong> ${request.duration || '-'} يوم
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>تاريخ البداية:</strong> ${formatDate(request.startDate || request.start_date)}
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>تاريخ النهاية:</strong> ${formatDate(request.endDate || request.end_date)}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // بيانات القائم بالأعمال
    detailsHTML += `
        <div class="card mb-3">
            <div class="card-header bg-light">
                <h5 class="mb-0">بيانات القائم بالأعمال</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-2">
                        <strong>الاسم:</strong> ${request.deputyName || request.deputy_name || '-'}
                    </div>
                    <div class="col-md-6 mb-2">
                        <strong>المنصب:</strong> ${request.deputyPosition || request.deputy_position || '-'}
                    </div>
                    <div class="col-md-6 mb-2">
                        <strong>رقم الهاتف:</strong> ${request.deputyPhone || request.deputy_phone || '-'}
                    </div>
                </div>
            </div>
        </div>
    `;

    // عرض المحتوى
    document.getElementById('requestDetailsContent').innerHTML = detailsHTML;
}

// دالة للحصول على اسم نوع الرحلة
function getTravelTypeName(type) {
    switch (type) {
        case 'work': return 'رحلة عمل';
        case 'medical': return 'رحلة علاج';
        case 'private': return 'رحلة خاصة';
        default: return type || '';
    }
}

// دالة للحصول على اسم نوع الإجازة
function getVacationTypeName(type) {
    switch (type) {
        case 'annual': return 'سنوية';
        case 'sick': return 'مرضية';
        case 'emergency': return 'اضطرارية';
        case 'exceptional': return 'استثنائية';
        default: return type || '';
    }
}

// دالة لتعديل طلب
function editRequest(requestId, requestType) {
    try {
        console.log(`تعديل الطلب رقم ${requestId} من نوع ${requestType}`);

        // تحديد نوع الطلب وعنوان API المناسب
        const apiUrl = requestType === 'travel'
            ? `/api/travel-requests/${requestId}`
            : `/api/vacation-requests/${requestId}`;

        // عرض مؤشر التحميل
        showAlert('جاري تحميل بيانات الطلب للتعديل...', 'info');

        // جلب بيانات الطلب من الخادم
        fetch(apiUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error('فشل في جلب بيانات الطلب');
                }
                return response.json();
            })
            .then(data => {
                if (!data.data) {
                    throw new Error('لم يتم العثور على بيانات الطلب');
                }

                const request = data.data;

                // تحديد النموذج المناسب
                if (requestType === 'travel') {
                    // عرض نموذج طلب السفر
                    showTravelForm();

                    // ملء النموذج ببيانات الطلب
                    setTimeout(() => {
                        const form = document.getElementById('travelForm');
                        if (form) {
                            // تحديد المدير
                            const managerSelect = form.querySelector('select[name="fullName"]');
                            if (managerSelect) {
                                // البحث عن الخيار المناسب
                                for (let i = 0; i < managerSelect.options.length; i++) {
                                    if (managerSelect.options[i].value == request.manager_id) {
                                        managerSelect.selectedIndex = i;
                                        break;
                                    }
                                }

                                // تحديث بيانات المدير
                                updateManagerInfo(managerSelect, 'travel');
                            }

                            // ملء باقي البيانات
                            form.querySelector('input[name="travelCountry"]').value = request.travel_country || '';
                            form.querySelector('input[name="notificationDate"]').value = request.notification_date ? request.notification_date.split('T')[0] : '';
                            form.querySelector('input[name="departureDate"]').value = request.departure_date ? request.departure_date.split('T')[0] : '';
                            form.querySelector('input[name="returnDate"]').value = request.return_date ? request.return_date.split('T')[0] : '';

                            const travelTypeSelect = form.querySelector('select[name="travelType"]');
                            if (travelTypeSelect) {
                                travelTypeSelect.value = request.travel_type || '';
                            }

                            form.querySelector('textarea[name="travelPurpose"]').value = request.travel_purpose || '';
                            form.querySelector('input[name="deputyName"]').value = request.deputy_name || '';
                            form.querySelector('input[name="deputyPosition"]').value = request.deputy_position || '';
                            form.querySelector('input[name="deputyPhone"]').value = request.deputy_phone || '';

                            // إضافة معرف الطلب كحقل مخفي
                            let requestIdInput = form.querySelector('input[name="requestId"]');
                            if (!requestIdInput) {
                                requestIdInput = document.createElement('input');
                                requestIdInput.type = 'hidden';
                                requestIdInput.name = 'requestId';
                                form.appendChild(requestIdInput);
                            }
                            requestIdInput.value = request.id;

                            // تغيير نص زر التقديم
                            const submitButton = form.querySelector('button[type="submit"]');
                            if (submitButton) {
                                submitButton.innerHTML = '<i class="fas fa-save me-2"></i>حفظ التعديلات';
                            }

                            // تغيير وظيفة الزر لتحديث الطلب بدلاً من إنشاء طلب جديد
                            form.onsubmit = function(e) {
                                e.preventDefault();
                                updateTravelRequest(request.id);
                                return false;
                            };

                            // عرض رسالة
                            showAlert('تم تحميل بيانات الطلب للتعديل', 'success');
                        }
                    }, 500);

                } else {
                    // عرض نموذج طلب الإجازة
                    showVacationForm();

                    // ملء النموذج ببيانات الطلب
                    setTimeout(() => {
                        const form = document.getElementById('vacationForm');
                        if (form) {
                            // تحديد المدير
                            const managerSelect = form.querySelector('select[name="fullName"]');
                            if (managerSelect) {
                                // البحث عن الخيار المناسب
                                for (let i = 0; i < managerSelect.options.length; i++) {
                                    if (managerSelect.options[i].value == request.manager_id) {
                                        managerSelect.selectedIndex = i;
                                        break;
                                    }
                                }

                                // تحديث بيانات المدير
                                updateManagerInfo(managerSelect, 'vacation');
                            }

                            // ملء باقي البيانات
                            const vacationTypeSelect = form.querySelector('select[name="vacationType"]');
                            if (vacationTypeSelect) {
                                vacationTypeSelect.value = request.vacation_type || '';
                            }

                            form.querySelector('input[name="duration"]').value = request.duration || '';
                            form.querySelector('input[name="startDate"]').value = request.start_date ? request.start_date.split('T')[0] : '';
                            form.querySelector('input[name="endDate"]').value = request.end_date ? request.end_date.split('T')[0] : '';
                            form.querySelector('input[name="deputyName"]').value = request.deputy_name || '';
                            form.querySelector('input[name="deputyPosition"]').value = request.deputy_position || '';
                            form.querySelector('input[name="deputyPhone"]').value = request.deputy_phone || '';

                            // إضافة معرف الطلب كحقل مخفي
                            let requestIdInput = form.querySelector('input[name="requestId"]');
                            if (!requestIdInput) {
                                requestIdInput = document.createElement('input');
                                requestIdInput.type = 'hidden';
                                requestIdInput.name = 'requestId';
                                form.appendChild(requestIdInput);
                            }
                            requestIdInput.value = request.id;

                            // تغيير نص زر التقديم
                            const submitButton = form.querySelector('button[type="submit"]');
                            if (submitButton) {
                                submitButton.innerHTML = '<i class="fas fa-save me-2"></i>حفظ التعديلات';
                            }

                            // تغيير وظيفة الزر لتحديث الطلب بدلاً من إنشاء طلب جديد
                            form.onsubmit = function(e) {
                                e.preventDefault();
                                updateVacationRequest(request.id);
                                return false;
                            };

                            // عرض رسالة
                            showAlert('تم تحميل بيانات الطلب للتعديل', 'success');
                        }
                    }, 500);
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل بيانات الطلب للتعديل:', error);
                showAlert('حدث خطأ في تحميل بيانات الطلب للتعديل: ' + error.message, 'danger');
            });

    } catch (err) {
        console.error('خطأ في تعديل الطلب:', err);
        showAlert('حدث خطأ في تعديل الطلب', 'danger');
    }
}

// دالة لطباعة تفاصيل الطلب
function printRequestDetails() {
    const detailsContent = document.getElementById('requestDetailsContent');
    if (!detailsContent) return;

    const printWindow = window.open('', '_blank');

    printWindow.document.write(`
        <html dir="rtl">
        <head>
            <title>تفاصيل الطلب</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
            <style>
                @media print {
                    .no-print { display: none; }
                    .card { border: 1px solid #ddd; margin-bottom: 20px; break-inside: avoid; }
                    .card-header { background-color: #f8f9fa !important; -webkit-print-color-adjust: exact; }
                }
                body { font-family: 'Arial', sans-serif; padding: 20px; }
            </style>
        </head>
        <body>
            <div class="container mt-4">
                <div class="text-center mb-4">
                    <h2>تفاصيل الطلب</h2>
                    <p class="text-muted">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit'
                    })}</p>
                </div>

                ${detailsContent.innerHTML}

                <div class="text-center mt-4 no-print">
                    <button class="btn btn-primary" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                </div>
            </div>
            <script>
                window.onload = function() {
                    setTimeout(function() {
                        window.print();
                    }, 500);
                };
            </script>
        </body>
        </html>
    `);

    printWindow.document.close();
}

// دالة لحذف طلب
function deleteRequest(requestId, requestType) {
    try {
        console.log(`حذف الطلب رقم ${requestId} من نوع ${requestType}`);

        // عرض نافذة تأكيد الحذف
        const modal = new bootstrap.Modal(document.getElementById('deleteRequestModal'));

        // تعيين معرف الطلب ونوعه في النافذة المنبثقة
        document.getElementById('deleteRequestId').textContent = requestId;
        document.getElementById('deleteRequestIdInput').value = requestId;
        document.getElementById('deleteRequestTypeInput').value = requestType;

        // عرض النافذة المنبثقة
        modal.show();

    } catch (err) {
        console.error('خطأ في عرض نافذة تأكيد الحذف:', err);
        showAlert('حدث خطأ في عرض نافذة تأكيد الحذف', 'danger');
    }
}

// دالة تأكيد حذف الطلب
function confirmDeleteRequest() {
    try {
        const requestId = document.getElementById('deleteRequestIdInput').value;
        const requestType = document.getElementById('deleteRequestTypeInput').value;

        console.log(`تأكيد حذف الطلب رقم ${requestId} من نوع ${requestType}`);

        // تحديد نوع الطلب للحذف
        console.log(`نوع الطلب للحذف: ${requestType}`);

        // عرض مؤشر التحميل
        const deleteConfirmButton = document.getElementById('deleteConfirmButton');
        const originalButtonText = deleteConfirmButton.innerHTML;
        deleteConfirmButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري الحذف...';
        deleteConfirmButton.disabled = true;

        // محاولة حذف الطلب من localStorage
        try {
            if (requestType === 'travel') {
                let travelRequests = JSON.parse(localStorage.getItem('travelRequests') || '[]');
                travelRequests = travelRequests.filter(req => req.id != requestId);
                localStorage.setItem('travelRequests', JSON.stringify(travelRequests));
            } else {
                let vacationRequests = JSON.parse(localStorage.getItem('vacationRequests') || '[]');
                vacationRequests = vacationRequests.filter(req => req.id != requestId);
                localStorage.setItem('vacationRequests', JSON.stringify(vacationRequests));
            }

            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteRequestModal'));
            modal.hide();

            // تحديث جدول الطلبات
            updateRequestsEntryTable();

            // عرض رسالة نجاح
            showAlert('تم حذف الطلب بنجاح', 'success');
        } catch (error) {
            console.error('خطأ في حذف الطلب من localStorage:', error);
            showAlert('حدث خطأ في حذف الطلب: ' + error.message, 'danger');
        }

        // إعادة زر التأكيد إلى حالته الأصلية
        deleteConfirmButton.innerHTML = originalButtonText;
        deleteConfirmButton.disabled = false;

    } catch (err) {
        console.error('خطأ في حذف الطلب:', err);
        showAlert('حدث خطأ في حذف الطلب', 'danger');
    }
}



// دالة لتنسيق التاريخ للإدخال في حقول التاريخ
function formatDateForInput(dateString) {
    if (!dateString) return '';
    try {
        const date = dateString instanceof Date ? dateString : new Date(dateString);
        return date.toISOString().split('T')[0];
    } catch (e) {
        return dateString;
    }
}

// دالة لتحديث طلب سفر
function updateTravelRequest(requestId) {
    try {
        const form = document.getElementById('travelForm');

        if (!form.checkValidity()) {
            form.classList.add('was-validated');
            return;
        }

        // الحصول على بيانات النموذج
        const managerId = form.querySelector('select[name="fullName"]').value;
        const travelCountry = form.querySelector('input[name="travelCountry"]').value;
        const notificationDate = form.querySelector('input[name="notificationDate"]').value;
        const departureDate = form.querySelector('input[name="departureDate"]').value;
        const returnDate = form.querySelector('input[name="returnDate"]').value;
        const travelType = form.querySelector('select[name="travelType"]').value;
        const travelPurpose = form.querySelector('textarea[name="travelPurpose"]').value;
        const deputyName = form.querySelector('input[name="deputyName"]').value;
        const deputyPosition = form.querySelector('input[name="deputyPosition"]').value;
        const deputyPhone = form.querySelector('input[name="deputyPhone"]').value;

        // إنشاء كائن الطلب للإرسال إلى الخادم
        const requestData = {
            manager_id: managerId,
            travel_country: travelCountry,
            notification_date: notificationDate,
            departure_date: departureDate,
            return_date: returnDate,
            travel_type: travelType,
            travel_purpose: travelPurpose,
            deputy_name: deputyName,
            deputy_position: deputyPosition,
            deputy_phone: deputyPhone
        };

        // عرض مؤشر التحميل
        const submitButton = form.querySelector('button[type="submit"]');
        const originalButtonText = submitButton.innerHTML;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري الحفظ...';
        submitButton.disabled = true;

        // إرسال الطلب إلى الخادم
        fetch(`/api/travel-requests/${requestId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('فشل في تحديث طلب السفر');
            }
            return response.json();
        })
        .then(data => {
            // إعادة تعيين النموذج
            form.reset();
            form.classList.remove('was-validated');

            // عرض رسالة نجاح
            showAlert('تم تحديث طلب السفر بنجاح', 'success');

            // العودة إلى الصفحة الرئيسية
            showHome();

            // تحديث جدول الطلبات
            updateRequestsTable();
        })
        .catch(error => {
            console.error('خطأ في تحديث طلب السفر:', error);
            showAlert('حدث خطأ في تحديث طلب السفر: ' + error.message, 'danger');
        })
        .finally(() => {
            // إعادة زر التقديم إلى حالته الأصلية
            submitButton.innerHTML = originalButtonText;
            submitButton.disabled = false;
        });

    } catch (error) {
        console.error('خطأ في تحديث طلب السفر:', error);
        showAlert('حدث خطأ في تحديث طلب السفر', 'danger');
    }
}

// دالة لتحديث طلب إجازة
function updateVacationRequest(requestId) {
    try {
        const form = document.getElementById('vacationForm');

        if (!form.checkValidity()) {
            form.classList.add('was-validated');
            return;
        }

        // الحصول على بيانات النموذج
        const managerId = form.querySelector('select[name="fullName"]').value;
        const vacationType = form.querySelector('select[name="vacationType"]').value;
        const duration = form.querySelector('input[name="duration"]').value;
        const startDate = form.querySelector('input[name="startDate"]').value;
        const endDate = form.querySelector('input[name="endDate"]').value;
        const deputyName = form.querySelector('input[name="deputyName"]').value;
        const deputyPosition = form.querySelector('input[name="deputyPosition"]').value;
        const deputyPhone = form.querySelector('input[name="deputyPhone"]').value;

        // إنشاء كائن الطلب للإرسال إلى الخادم
        const requestData = {
            manager_id: managerId,
            vacation_type: vacationType,
            duration: duration,
            start_date: startDate,
            end_date: endDate,
            deputy_name: deputyName,
            deputy_position: deputyPosition,
            deputy_phone: deputyPhone
        };

        // عرض مؤشر التحميل
        const submitButton = form.querySelector('button[type="submit"]');
        const originalButtonText = submitButton.innerHTML;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري الحفظ...';
        submitButton.disabled = true;

        // إرسال الطلب إلى الخادم
        fetch(`/api/vacation-requests/${requestId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('فشل في تحديث طلب الإجازة');
            }
            return response.json();
        })
        .then(data => {
            // إعادة تعيين النموذج
            form.reset();
            form.classList.remove('was-validated');

            // عرض رسالة نجاح
            showAlert('تم تحديث طلب الإجازة بنجاح', 'success');

            // العودة إلى الصفحة الرئيسية
            showHome();

            // تحديث جدول الطلبات
            updateRequestsTable();
        })
        .catch(error => {
            console.error('خطأ في تحديث طلب الإجازة:', error);
            showAlert('حدث خطأ في تحديث طلب الإجازة: ' + error.message, 'danger');
        })
        .finally(() => {
            // إعادة زر التقديم إلى حالته الأصلية
            submitButton.innerHTML = originalButtonText;
            submitButton.disabled = false;
        });

    } catch (error) {
        console.error('خطأ في تحديث طلب الإجازة:', error);
        showAlert('حدث خطأ في تحديث طلب الإجازة', 'danger');
    }
}

// دالة لتهيئة أزرار التحكم في لوحة المدراء الأكثر إجازة
function initializeVacationManagersButtons() {
    // إضافة معالجات الأحداث لأزرار التحكم في لوحة المدراء الأكثر إجازة
    const resetVacationDataBtn = document.getElementById('resetVacationDataBtn');
    if (resetVacationDataBtn) {
        resetVacationDataBtn.addEventListener('click', async () => {
            if (confirm('هل أنت متأكد من رغبتك في إعادة تعيين بيانات الإجازات؟ سيتم حذف جميع طلبات الإجازة الموجودة.')) {
                try {
                    resetVacationDataBtn.disabled = true;
                    resetVacationDataBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';

                    const response = await fetch('/api/reset-database', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    const result = await response.json();

                    if (response.ok) {
                        alert(`تم إعادة تعيين البيانات بنجاح: ${result.message}`);
                        // إعادة تحميل البيانات
                        updateMostVacationingManagersTable();
                    } else {
                        alert(`فشل في إعادة تعيين البيانات: ${result.message || 'خطأ غير معروف'}`);
                    }
                } catch (error) {
                    console.error('خطأ في إعادة تعيين البيانات:', error);
                    alert('حدث خطأ أثناء إعادة تعيين البيانات. يرجى المحاولة مرة أخرى.');
                } finally {
                    resetVacationDataBtn.disabled = false;
                    resetVacationDataBtn.innerHTML = '<i class="fas fa-sync-alt"></i>';
                }
            }
        });
    }

    const refreshVacationDataBtn = document.getElementById('refreshVacationDataBtn');
    if (refreshVacationDataBtn) {
        refreshVacationDataBtn.addEventListener('click', () => {
            refreshVacationDataBtn.disabled = true;
            refreshVacationDataBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';

            // إعادة تحميل البيانات
            updateMostVacationingManagersTable().finally(() => {
                refreshVacationDataBtn.disabled = false;
                refreshVacationDataBtn.innerHTML = '<i class="fas fa-redo-alt"></i>';
            });
        });
    }
}

// دالة لإلغاء سفر مدير
function cancelTravel(requestId) {
    if (confirm('هل أنت متأكد من رغبتك في إلغاء سفر هذا المدير؟')) {
        try {
            console.log('إلغاء سفر المدير رقم:', requestId);

            // عرض مؤشر التحميل
            showAlert('جاري إلغاء السفر...', 'info');

            // إرسال طلب إلى الخادم لإلغاء السفر
            fetch(`/api/travel-requests/${requestId}/cancel`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('فشل في إلغاء السفر');
                }
                return response.json();
            })
            .then(result => {
                console.log('تم إلغاء السفر بنجاح:', result);
                showAlert('تم إلغاء السفر بنجاح', 'success');

                // تحديث جدول المدراء المسافرين
                updateTravelingManagersTable();
            })
            .catch(error => {
                console.error('خطأ في إلغاء السفر:', error);
                showAlert('حدث خطأ أثناء إلغاء السفر: ' + error.message, 'danger');
            });
        } catch (error) {
            console.error('خطأ في إلغاء السفر:', error);
            showAlert('حدث خطأ أثناء إلغاء السفر', 'danger');
        }
    }
}

// دالة لتمديد سفر مدير
function extendTravel(requestId) {
    try {
        console.log('تمديد سفر المدير رقم:', requestId);

        // جلب بيانات طلب السفر
        fetch(`/api/travel-requests/${requestId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('فشل في جلب بيانات طلب السفر');
            }
            return response.json();
        })
        .then(data => {
            if (!data.data) {
                throw new Error('لم يتم العثور على بيانات الطلب');
            }

            const request = data.data;

            // إنشاء نافذة منبثقة لتمديد السفر
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.id = 'extendTravelModal';
            modal.setAttribute('tabindex', '-1');
            modal.setAttribute('aria-labelledby', 'extendTravelModalLabel');
            modal.setAttribute('aria-hidden', 'true');

            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="extendTravelModalLabel">تمديد سفر المدير: ${request.manager_name || request.fullName}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="extendTravelForm">
                                <input type="hidden" name="requestId" value="${request.id}">

                                <div class="mb-3">
                                    <label class="form-label">تاريخ العودة الحالي</label>
                                    <input type="date" class="form-control" value="${request.return_date || request.returnDate}" readonly>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">تاريخ العودة الجديد <span class="text-danger">*</span></label>
                                    <input type="date" name="newReturnDate" class="form-control" required min="${request.return_date || request.returnDate}">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">سبب التمديد <span class="text-danger">*</span></label>
                                    <textarea name="extensionReason" class="form-control" rows="3" required></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="submitTravelExtension()">تأكيد التمديد</button>
                        </div>
                    </div>
                </div>
            `;

            // إضافة النافذة المنبثقة إلى الصفحة
            document.body.appendChild(modal);

            // عرض النافذة المنبثقة
            const modalInstance = new bootstrap.Modal(document.getElementById('extendTravelModal'));
            modalInstance.show();

            // إزالة النافذة المنبثقة عند إغلاقها
            document.getElementById('extendTravelModal').addEventListener('hidden.bs.modal', function() {
                document.body.removeChild(modal);
            });
        })
        .catch(error => {
            console.error('خطأ في جلب بيانات طلب السفر:', error);
            showAlert('حدث خطأ أثناء جلب بيانات طلب السفر: ' + error.message, 'danger');
        });
    } catch (error) {
        console.error('خطأ في تمديد السفر:', error);
        showAlert('حدث خطأ أثناء تمديد السفر', 'danger');
    }
}

// دالة لتقديم طلب تمديد السفر
function submitTravelExtension() {
    try {
        const form = document.getElementById('extendTravelForm');

        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const requestId = form.querySelector('input[name="requestId"]').value;
        const newTravelEndDate = form.querySelector('input[name="newTravelEndDate"]').value; // تم التعديل هنا

        // عرض مؤشر التحميل
        const submitButton = document.querySelector('#extendTravelModal .btn-primary');
        const originalButtonText = submitButton.innerHTML;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري التمديد...';
        submitButton.disabled = true;

        // إرسال طلب إلى الخادم لتمديد السفر
        fetch(`/api/travel-requests/${requestId}/extend`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                newReturnDate: newTravelEndDate // إرسال newTravelEndDate كـ newReturnDate للخادم
                // extensionReason تم إزالته
            })
        })
        .then(response => {
            if (!response.ok) {
                // محاولة قراءة نص الخطأ من الاستجابة
                return response.text().then(text => { 
                    throw new Error(text || 'فشل في تمديد السفر'); 
                });
            }
            return response.json();
        })
        .then(result => {
            console.log('تم تمديد السفر بنجاح:', result);

            // إغلاق النافذة المنبثقة
            const modalElement = document.getElementById('extendTravelModal');
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }

            showAlert('تم تمديد السفر بنجاح', 'success');

            // تحديث تبويب المدراء المسافرين
            updateTravelingTab(); // تم التعديل هنا
        })
        .catch(error => {
            console.error('خطأ في تمديد السفر:', error);
            showAlert('حدث خطأ أثناء تمديد السفر: ' + error.message, 'danger');

            // إعادة زر التقديم إلى حالته الأصلية
            submitButton.innerHTML = originalButtonText;
            submitButton.disabled = false;
        });
    } catch (error) {
        console.error('خطأ في تقديم طلب تمديد السفر:', error);
        showAlert('حدث خطأ أثناء تقديم طلب تمديد السفر', 'danger');
    }
}

// دالة لإلغاء إجازة مدير
function cancelVacation(requestId) {
    if (confirm('هل أنت متأكد من رغبتك في إلغاء إجازة هذا المدير؟')) {
        try {
            console.log('إلغاء إجازة المدير رقم:', requestId);

            // عرض مؤشر التحميل
            showAlert('جاري إلغاء الإجازة...', 'info');

            // إرسال طلب إلى الخادم لإلغاء الإجازة
            fetch(`/api/vacation-requests/${requestId}/cancel`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('فشل في إلغاء الإجازة');
                }
                return response.json();
            })
            .then(result => {
                console.log('تم إلغاء الإجازة بنجاح:', result);
                showAlert('تم إلغاء الإجازة بنجاح', 'success');

                // تحديث جدول المدراء في إجازة
                updateVacationingTab();
            })
            .catch(error => {
                console.error('خطأ في إلغاء الإجازة:', error);
                showAlert('حدث خطأ أثناء إلغاء الإجازة: ' + error.message, 'danger');
            });
        } catch (error) {
            console.error('خطأ في إلغاء الإجازة:', error);
            showAlert('حدث خطأ أثناء إلغاء الإجازة', 'danger');
        }
    }
}

// دالة لتمديد إجازة مدير
function extendVacation(requestId) {
    try {
        console.log('تمديد إجازة المدير رقم:', requestId);

        // جلب بيانات طلب الإجازة
        fetch(`/api/vacation-requests/${requestId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('فشل في جلب بيانات طلب الإجازة');
            }
            return response.json();
        })
        .then(data => {
            if (!data.data) {
                throw new Error('لم يتم العثور على بيانات الطلب');
            }

            const request = data.data;

            // إنشاء نافذة منبثقة لتمديد الإجازة
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.id = 'extendVacationModal';
            modal.setAttribute('tabindex', '-1');
            modal.setAttribute('aria-labelledby', 'extendVacationModalLabel');
            modal.setAttribute('aria-hidden', 'true');

            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="extendVacationModalLabel">تمديد إجازة المدير: ${request.manager_name || request.fullName}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="extendVacationForm">
                                <input type="hidden" name="requestId" value="${request.id}">

                                <div class="mb-3">
                                    <label class="form-label">تاريخ نهاية الإجازة الحالي</label>
                                    <input type="date" class="form-control" value="${request.end_date || request.endDate}" readonly>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">تاريخ نهاية الإجازة الجديد <span class="text-danger">*</span></label>
                                    <input type="date" name="newEndDate" class="form-control" required min="${request.end_date || request.endDate}">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">سبب التمديد <span class="text-danger">*</span></label>
                                    <textarea name="extensionReason" class="form-control" rows="3" required></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="submitVacationExtension()">تأكيد التمديد</button>
                        </div>
                    </div>
                </div>
            `;

            // إضافة النافذة المنبثقة إلى الصفحة
            document.body.appendChild(modal);

            // عرض النافذة المنبثقة
            const modalInstance = new bootstrap.Modal(document.getElementById('extendVacationModal'));
            modalInstance.show();

            // إزالة النافذة المنبثقة عند إغلاقها
            document.getElementById('extendVacationModal').addEventListener('hidden.bs.modal', function() {
                document.body.removeChild(modal);
            });
        })
        .catch(error => {
            console.error('خطأ في جلب بيانات طلب الإجازة:', error);
            showAlert('حدث خطأ أثناء جلب بيانات طلب الإجازة: ' + error.message, 'danger');
        });
    } catch (error) {
        console.error('خطأ في تمديد الإجازة:', error);
        showAlert('حدث خطأ أثناء تمديد الإجازة', 'danger');
    }
}

// دالة لتقديم طلب تمديد الإجازة
// دالة لتقديم طلب تمديد الإجازة
function submitVacationExtension() {
    try {
        const form = document.getElementById('extendVacationForm');

        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const requestId = form.querySelector('input[name="requestId"]').value;
        const newVacationEndDate = form.querySelector('input[name="newVacationEndDate"]').value; // تم التعديل هنا

        // عرض مؤشر التحميل
        const submitButton = document.querySelector('#extendVacationModal .btn-primary');
        const originalButtonText = submitButton.innerHTML;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري التمديد...';
        submitButton.disabled = true;

        // إرسال طلب إلى الخادم لتمديد الإجازة
        fetch(`/api/vacation-requests/${requestId}/extend`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                newEndDate: newVacationEndDate // إرسال newVacationEndDate كـ newEndDate للخادم
                // extensionReason تم إزالته
            })
        })
        .then(response => {
            if (!response.ok) {
                // محاولة قراءة نص الخطأ من الاستجابة
                return response.text().then(text => { 
                    throw new Error(text || 'فشل في تمديد الإجازة'); 
                });
            }
            return response.json();
        })
        .then(result => {
            console.log('تم تمديد الإجازة بنجاح:', result);

            // إغلاق النافذة المنبثقة
            const modalElement = document.getElementById('extendVacationModal');
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }

            showAlert('تم تمديد الإجازة بنجاح', 'success');

            // تحديث جدول المدراء في إجازة
            updateVacationingTab();
        })
        .catch(error => {
            console.error('خطأ في تمديد الإجازة:', error);
            showAlert('حدث خطأ أثناء تمديد الإجازة: ' + error.message, 'danger');

            // إعادة زر التقديم إلى حالته الأصلية
            submitButton.innerHTML = originalButtonText;
            submitButton.disabled = false;
        });
    } catch (error) {
        console.error('خطأ في تقديم طلب تمديد الإجازة:', error);
        showAlert('حدث خطأ أثناء تقديم طلب تمديد الإجازة', 'danger');
    }
}

// دالة لعرض نافذة تمديد السفر
function showExtendTravelModal(requestId) {
    console.log(`showExtendTravelModal called for requestId: ${requestId}`);
    const modalElement = document.getElementById('extendTravelModal');
    if (modalElement) {
        // تعبئة حقل requestId المخفي
        const requestIdInput = modalElement.querySelector('#extendTravelRequestId');
        if (requestIdInput) {
            requestIdInput.value = requestId;
        } else {
            console.error('Request ID input field not found in extend travel modal');
            showAlert('حقل معرف الطلب غير موجود في نموذج تمديد السفر.', 'danger');
            return;
        }

        // مسح أي قيمة سابقة في حقل التاريخ
        const newDateInput = modalElement.querySelector('#newTravelEndDate');
        if (newDateInput) {
            newDateInput.value = '';
        }

        // عرض النافذة المنبثقة
        const modalInstance = new bootstrap.Modal(modalElement);
        modalInstance.show();
    } else {
        console.error('Extend travel modal not found');
        showAlert('نموذج تمديد السفر غير موجود.', 'danger');
    }
}

// دالة لعرض نافذة تمديد الإجازة
function showExtendVacationModal(requestId) {
    console.log(`showExtendVacationModal called for requestId: ${requestId}`);
    const modalElement = document.getElementById('extendVacationModal');
    if (modalElement) {
        // تعبئة حقل requestId المخفي
        const requestIdInput = modalElement.querySelector('#extendVacationRequestId');
        if (requestIdInput) {
            requestIdInput.value = requestId;
        } else {
            console.error('Request ID input field not found in extend vacation modal');
            showAlert('حقل معرف الطلب غير موجود في نموذج تمديد الإجازة.', 'danger');
            return;
        }

        // مسح أي قيمة سابقة في حقل التاريخ
        const newDateInput = modalElement.querySelector('#newVacationEndDate');
        if (newDateInput) {
            newDateInput.value = '';
        }

        // عرض النافذة المنبثقة
        const modalInstance = new bootstrap.Modal(modalElement);
        modalInstance.show();
    } else {
        console.error('Extend vacation modal not found');
        showAlert('نموذج تمديد الإجازة غير موجود.', 'danger');
    }
}

// استدعاء دالة تهيئة الأزرار عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة أزرار التحكم في لوحة المدراء الأكثر إجازة
    initializeVacationManagersButtons();

    // تحديث لوحة المدراء الغائبين حاليا
    console.log('تحديث لوحة المدراء الغائبين عند تحميل الصفحة');

    // تحديث عدادات الطلبات
    updateRequestsCounters();

    // تحديث لوحات المعلومات في الصفحة الرئيسية
    updateDashboardStats();

    // تحديث جدول المدراء المسافرين حاليا
    updateTravelingManagersTable();

    // تحديث عدادات المدراء الغائبين
    updateAbsentManagersCounters();
});





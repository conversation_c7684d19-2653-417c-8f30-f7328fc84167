<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة طلبات السفر والإجازات | لوحة التحكم</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="users-management.css">
    <link rel="stylesheet" href="layout-fix.css">
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <!-- اسم النظام (في البداية من اليمين) -->
            <a class="navbar-brand" href="#">
                <i class="fas fa-building-shield ms-2"></i>
                نظام إدارة طلبات السفر والإجازات لاعضاء السلطة المحلية
            </a>

            <!-- زر القائمة للشاشات الصغيرة -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- محتوى القائمة -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- روابط التنقل الرئيسية -->
                <ul class="navbar-nav main-nav">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" data-section="homeSection" onclick="showSection('homeSection')">
                            <i class="fas fa-home"></i>
                            <span>الرئيسية</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-section="managersSettingsSection" onclick="showSection('managersSettingsSection')">
                            <i class="fas fa-users-gear"></i>
                            <span>إدارة المدراء</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-section="requestsEntrySection" onclick="showSection('requestsEntrySection')">
                            <i class="fas fa-file-circle-plus"></i>
                            <span>إدخال الطلبات</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-section="reportsSection" onclick="showSection('reportsSection')">
                            <i class="fas fa-chart-column"></i>
                            <span>التقارير</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" data-section="usersManagementSection" onclick="showSection('usersManagementSection')">
                            <i class="fas fa-users-cog"></i>
                            <span>المستخدمين</span>
                        </a>
                    </li>
                </ul>

                <!-- أيقونات المستخدم والتنبيهات -->
                <div class="user-controls">
                    <!-- التنبيهات -->
                    <div class="notifications-container">
                        <button class="btn btn-link text-light position-relative" onclick="toggleNotifications()">
                            <i class="fas fa-bell"></i>
                            <span id="notificationsBadge" class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="display: none;">
                                0
                            </span>
                        </button>
                        <!-- قائمة التنبيهات -->
                        <div id="notificationsMenu" class="notifications-menu" style="display: none;">
                            <div class="notifications-header">
                                <h6 class="mb-0">التنبيهات</h6>
                            </div>
                            <div id="notificationsList" class="notifications-list">
                                <!-- سيتم إضافة التنبيهات هنا ديناميكياً -->
                            </div>
                        </div>
                    </div>

                    <!-- معلومات المستخدم -->
                    <div class="dropdown">
                        <a class="text-light dropdown-toggle text-decoration-none" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-circle"></i>
                            <span id="userInfo">مرحباً، المدير</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item active" id="currentSection" aria-current="page">لوحة التحكم</li>
            </ol>
        </nav>

        <!-- Home Section -->
        <div id="homeSection" class="content-section">
            <div class="row g-3 my-2">
                <div class="col-md-4">
                    <div class="p-3 bg-white shadow-sm d-flex justify-content-around align-items-center rounded">
                        <div>
                            <h3 class="fs-2" id="travelRequestsCount">0</h3>
                            <p class="fs-5">طلبات السفر</p>
                        </div>
                        <i class="fas fa-plane-departure fs-1 primary-text border rounded-full secondary-bg p-3"></i>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="p-3 bg-white shadow-sm d-flex justify-content-around align-items-center rounded">
                        <div>
                            <h3 class="fs-2" id="vacationRequestsCount">0</h3>
                            <p class="fs-5">طلبات الإجازة</p>
                        </div>
                        <i class="fas fa-calendar-alt fs-1 primary-text border rounded-full secondary-bg p-3"></i>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="p-3 bg-white shadow-sm d-flex justify-content-around align-items-center rounded">
                        <div>
                            <h3 class="fs-2" id="managersCount">0</h3>
                            <p class="fs-5">المدراء المسجلين</p>
                        </div>
                        <i class="fas fa-users fs-1 primary-text border rounded-full secondary-bg p-3"></i>
                    </div>
                </div>
            </div>



            <!-- لوحة المدراء الغائبين حاليا -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-user-clock me-2"></i>المدراء الغائبين حاليا</h5>
                            <ul class="nav nav-tabs card-header-tabs mt-2" id="absentManagersTabs">
                                <li class="nav-item">
                                    <a class="nav-link active" id="traveling-tab" data-bs-toggle="tab" href="#travelingTab">
                                        <i class="fas fa-plane me-1"></i> المسافرين
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="vacationing-tab" data-bs-toggle="tab" href="#vacationingTab">
                                        <i class="fas fa-umbrella-beach me-1"></i> في إجازة
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div class="card-body">
                            <div class="tab-content">
                                <!-- تبويب المدراء المسافرين -->
                                <div class="tab-pane fade show active" id="travelingTab">
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>اسم المدير</th>
                                                    <th>المنصب</th>
                                                    <th>جهة العمل</th>
                                                    <th>وجهة السفر</th>
                                                    <th>تاريخ العودة</th>
                                                    <th>القائم بالأعمال</th>
                                                    <th>رقم التواصل</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody id="travelingManagersTable">
                                                <tr>
                                                    <td colspan="7" class="text-center">جاري تحميل البيانات...</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- تبويب المدراء في إجازة -->
                                <div class="tab-pane fade" id="vacationingTab">
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>اسم المدير</th>
                                                    <th>المنصب</th>
                                                    <th>جهة العمل</th>
                                                    <th>نوع الإجازة</th>
                                                    <th>تاريخ العودة</th>
                                                    <th>القائم بالأعمال</th>
                                                    <th>رقم التواصل</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody id="vacationingManagersTable">
                                                <tr>
                                                    <td colspan="7" class="text-center">جاري تحميل البيانات...</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- لوحة إحصائيات المدراء -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>إحصائيات المدراء</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- قسم المدراء الأكثر سفراً -->
                                <div class="col-md-6">
                                    <div class="card border-0 shadow-sm h-100">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0 text-primary">
                                                <i class="fas fa-plane me-2"></i>المدراء الأكثر سفراً
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <!-- مخطط دائري للمدراء الأكثر سفراً -->
                                            <div class="text-center mb-3">
                                                <canvas id="travelChart" width="300" height="300"></canvas>
                                            </div>

                                            <!-- قائمة المدراء الأكثر سفراً -->
                                            <div id="mostTravelingManagersList">
                                                <div class="text-center py-4">
                                                    <div class="spinner-border text-primary" role="status">
                                                        <span class="visually-hidden">جاري التحميل...</span>
                                                    </div>
                                                    <p class="mt-2">جاري تحميل البيانات...</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- قسم المدراء الأكثر إجازةً -->
                                <div class="col-md-6">
                                    <div class="card border-0 shadow-sm h-100">
                                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0 text-success">
                                                <i class="fas fa-umbrella-beach me-2"></i>المدراء الأكثر إجازةً
                                            </h6>
                                            <div>
                                                <button id="resetVacationDataBtn" class="btn btn-sm btn-outline-danger" title="إعادة تعيين البيانات">
                                                    <i class="fas fa-sync-alt"></i>
                                                </button>
                                                <button id="refreshVacationDataBtn" class="btn btn-sm btn-outline-success ms-1" title="تحديث البيانات">
                                                    <i class="fas fa-redo-alt"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <!-- مخطط دائري للمدراء الأكثر إجازةً -->
                                            <div class="text-center mb-3">
                                                <canvas id="vacationChart" width="300" height="300"></canvas>
                                            </div>

                                            <!-- قائمة المدراء الأكثر إجازةً -->
                                            <div id="mostVacationingManagersList">
                                                <div class="text-center py-4">
                                                    <div class="spinner-border text-success" role="status">
                                                        <span class="visually-hidden">جاري التحميل...</span>
                                                    </div>
                                                    <p class="mt-2">جاري تحميل البيانات...</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>

        <!-- Users Management Section -->
        <div id="usersManagementSection" class="content-section" style="display: none;">
            <div class="container mt-4">
                <div class="row mb-4">
                    <div class="col">
                        <h1 class="mb-3">إدارة المستخدمين</h1>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <button id="addUserBtn" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#userFormModal">
                                <i class="fas fa-plus-circle me-2"></i>إضافة مستخدم جديد
                            </button>
                            <div class="input-group w-50">
                                <input type="text" id="searchInput" class="form-control" placeholder="بحث عن مستخدم...">
                                <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول المستخدمين -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">قائمة المستخدمين</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped">
                                <thead class="table-light">
                                    <tr>
                                        <th>#</th>
                                        <th>اسم المستخدم</th>
                                        <th>الاسم الكامل</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الصلاحية</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                    <!-- سيتم تعبئة البيانات هنا عن طريق JavaScript -->
                                    <tr>
                                        <td colspan="8" class="text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">جاري التحميل...</span>
                                            </div>
                                            <p class="mt-2">جاري تحميل بيانات المستخدمين...</p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <span id="totalUsers">إجمالي المستخدمين: <span class="badge bg-primary">0</span></span>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-secondary" id="refreshBtn">
                                    <i class="fas fa-sync-alt me-1"></i>تحديث
                                </button>
                                <button class="btn btn-sm btn-outline-primary" id="exportBtn">
                                    <i class="fas fa-file-export me-1"></i>تصدير
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- نموذج إضافة/تعديل مستخدم -->
                <div class="modal fade" id="userFormModal" tabindex="-1" aria-labelledby="userFormModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="userFormModalLabel">إضافة مستخدم جديد</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                            </div>
                            <div class="modal-body">
                                <form id="userForm" class="needs-validation" novalidate>
                                    <input type="hidden" id="userId" name="id">

                                    <div class="mb-3">
                                        <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="username" name="username" required>
                                        <div class="invalid-feedback">يرجى إدخال اسم المستخدم</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="fullName" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="fullName" name="full_name" required>
                                        <div class="invalid-feedback">يرجى إدخال الاسم الكامل</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="email" class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" id="email" name="email">
                                        <div class="invalid-feedback">يرجى إدخال بريد إلكتروني صحيح</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="password" class="form-label">كلمة المرور <span class="text-danger password-required">*</span></label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="password" name="password" required>
                                            <button class="btn btn-outline-secondary toggle-password" type="button">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        <div class="invalid-feedback">يرجى إدخال كلمة مرور</div>
                                        <div class="form-text">يجب أن تحتوي على 8 أحرف على الأقل وتتضمن أحرف كبيرة وصغيرة وأرقام</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="role" class="form-label">الصلاحية <span class="text-danger">*</span></label>
                                        <select class="form-select" id="role" name="role" required>
                                            <option value="">اختر الصلاحية...</option>
                                            <option value="admin">مدير النظام</option>
                                            <option value="editor">محرر</option>
                                            <option value="viewer">مشاهد</option>
                                            <option value="user">مستخدم عادي</option>
                                        </select>
                                        <div class="invalid-feedback">يرجى اختيار الصلاحية</div>
                                    </div>

                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="isActive" name="is_active" checked>
                                        <label class="form-check-label" for="isActive">مستخدم نشط</label>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" id="saveUserBtn">حفظ</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- نافذة تأكيد الحذف -->
                <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog modal-sm">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">تأكيد الحذف</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                            </div>
                            <div class="modal-body">
                                <p>هل أنت متأكد من رغبتك في حذف هذا المستخدم؟</p>
                                <p class="text-danger"><small>لا يمكن التراجع عن هذا الإجراء.</small></p>
                                <input type="hidden" id="deleteUserId">
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">حذف</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Travel Form Section -->
        <div id="travelFormSection" class="content-section" style="display: none;">
            <div class="section-header mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h3 class="section-title">
                            <i class="fas fa-plane-departure me-2"></i>نموذج طلب سفر
                        </h3>
                    </div>
                </div>
            </div>

            <form id="travelForm" class="needs-validation" novalidate>
                        <!-- بيانات مقدم الطلب -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-user me-2"></i>بيانات مقدم الطلب</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                                            <select name="fullName" class="form-select" required>
                                                <option value="">اختر المدير...</option>
                                                <!-- سيتم تعبئة هذه القائمة ديناميكياً -->
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">المنصب الحالي <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-briefcase"></i></span>
                                            <input type="text" name="position" class="form-control" required readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3" style="display: none;">
                                        <label class="form-label">القسم/الإدارة <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-building"></i></span>
                                            <input type="text" name="department" class="form-control" readonly>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تفاصيل السفر -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-plane me-2"></i>تفاصيل السفر</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">دولة السفر <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-globe"></i></span>
                                            <select name="travelCountry" class="form-select" required id="countriesList">
                                                <option value="">اختر دولة السفر...</option>
                                                <!-- سيتم تعبئة هذه القائمة ديناميكياً بجميع دول العالم -->
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ الإشعار <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                            <input type="date" name="notificationDate" class="form-control" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ السفر <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-plane-departure"></i></span>
                                            <input type="date" name="departureDate" class="form-control" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ العودة <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-plane-arrival"></i></span>
                                            <input type="date" name="returnDate" class="form-control" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">نوع الرحلة <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-tag"></i></span>
                                            <select name="travelType" class="form-select" required>
                                                <option value="">اختر نوع الرحلة</option>
                                                <option value="work">رحلة عمل</option>
                                                <option value="medical">رحلة علاج</option>
                                                <option value="private">رحلة خاصة</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">الغرض من السفر <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-align-left"></i></span>
                                            <textarea name="travelPurpose" class="form-control" rows="3" required></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- بيانات القائم بالأعمال -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-user-tie me-2"></i>بيانات القائم بالأعمال</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">اسم القائم بالأعمال <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                                            <input type="text" name="deputyName" class="form-control" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">المنصب الحالي <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-briefcase"></i></span>
                                            <input type="text" name="deputyPosition" class="form-control" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                            <input type="tel" name="deputyPhone" class="form-control" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- المرفقات -->
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-paperclip me-2"></i>المرفقات</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">النموذج المعبأ <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-file-upload"></i></span>
                                        <input type="file" name="mainAttachment" class="form-control" required>
                                    </div>
                                    <small class="text-muted">يرجى إرفاق النموذج بعد تعبئته</small>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">مرفقات إضافية</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-paperclip"></i></span>
                                        <input type="file" name="additionalAttachments" class="form-control" multiple>
                                    </div>
                                    <small class="text-muted">يمكنك إرفاق مستندات داعمة إضافية</small>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="d-flex gap-2 justify-content-between">
                            <button type="button" class="btn btn-info px-4" onclick="printEmptyForm('travel')">
                                <i class="fas fa-print me-2"></i>طباعة استمارة فارغة
                            </button>

                            <div>
                                <button type="button" class="btn btn-secondary px-4" onclick="showRequestsEntry()">
                                    <i class="fas fa-times me-2"></i>إلغاء
                                </button>
                                <button type="submit" class="btn btn-primary px-4">
                                    <i class="fas fa-paper-plane me-2"></i>تقديم الطلب
                                </button>
                            </div>
                        </div>
                    </form>
        </div>

        <!-- Vacation Form Section -->
        <div id="vacationFormSection" class="content-section" style="display: none;">
            <div class="section-header mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h3 class="section-title">
                            <i class="fas fa-calendar-alt me-2"></i>نموذج طلب إجازة
                        </h3>
                    </div>
                </div>
            </div>

            <form id="vacationForm" class="needs-validation" novalidate>
                <!-- بيانات مقدم الطلب -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="fas fa-user me-2"></i>بيانات مقدم الطلب</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الاسم الكامل</label>
                                <select name="fullName" class="form-select" required>
                                    <option value="">اختر المدير...</option>
                                    <!-- سيتم تعبئة هذه القائمة ديناميكياً -->
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المنصب الحالي</label>
                                <input type="text" name="position" class="form-control" required readonly>
                            </div>
                            <div class="col-md-6 mb-3" style="display: none;">
                                <label class="form-label">القسم/الإدارة</label>
                                <input type="text" name="department" class="form-control" readonly>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الإجازة -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>تفاصيل الإجازة</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">نوع الإجازة</label>
                                <select name="vacationType" class="form-select" required>
                                    <option value="">اختر نوع الإجازة</option>
                                    <option value="annual">سنوية</option>
                                    <option value="sick">مرضية</option>
                                    <option value="emergency">اضطرارية</option>
                                    <option value="exceptional">استثنائية</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">مدة الإجازة (بالأيام)</label>
                                <input type="number" name="duration" class="form-control" min="1" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">تاريخ بداية الإجازة</label>
                                <input type="date" name="startDate" class="form-control" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">تاريخ نهاية الإجازة</label>
                                <input type="date" name="endDate" class="form-control" required>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- بيانات القائم بالأعمال -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="fas fa-user-tie me-2"></i>بيانات القائم بالأعمال</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم القائم بالأعمال</label>
                                <input type="text" name="deputyName" class="form-control" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">المنصب الحالي</label>
                                <input type="text" name="deputyPosition" class="form-control" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" name="deputyPhone" class="form-control" required>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- المرفقات -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="fas fa-paperclip me-2"></i>المرفقات</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">المرفقات (إن وجدت)</label>
                            <input type="file" name="attachments" class="form-control" multiple>
                            <div class="form-text">يمكنك إرفاق التقارير الطبية أو أي مستندات داعمة</div>
                        </div>
                    </div>
                </div>

                <!-- أزرار التحكم -->
                <div class="d-flex gap-2 justify-content-between">
                    <button type="button" class="btn btn-info px-4" onclick="printEmptyForm('vacation')">
                        <i class="fas fa-print me-2"></i>طباعة استمارة فارغة
                    </button>

                    <div>
                        <button type="button" class="btn btn-secondary px-4" onclick="showRequestsEntry()">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </button>
                        <button type="submit" class="btn btn-primary px-4">
                            <i class="fas fa-paper-plane me-2"></i>تقديم الطلب
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Managers Settings Section -->
        <div id="managersSettingsSection" class="content-section" style="display: none;">
            <div class="section-header mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h3 class="section-title">
                            <i class="fas fa-users-cog text-primary me-2"></i>
                            إدارة المدراء والوكلاء
                        </h3>
                    </div>
                    <div class="col-auto">
                        <div class="btn-group mb-3 mt-2">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addManagerModal">
                                <i class="fas fa-plus"></i> إضافة مدير جديد
                            </button>
                            <button type="button" class="btn btn-success" onclick="exportManagersToExcel()">
                                <i class="fas fa-file-export"></i> تصدير Excel
                            </button>
                            <button type="button" class="btn btn-info" onclick="showImportManagersModal()">
                                <i class="fas fa-file-import"></i> استيراد Excel
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal نموذج إضافة مدير -->
            <div class="modal fade" id="addManagerModal" tabindex="-1" data-bs-backdrop="static">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إضافة مدير جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="addManagerForm" onsubmit="addManager(event)">
                                <div class="row">
                                    <!-- البيانات الشخصية -->
                                    <h4 class="mb-3">البيانات الشخصية</h4>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الاسم الكامل</label>
                                        <input type="text" class="form-control" id="managerFullName" name="full_name" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">مكان الميلاد</label>
                                        <input type="text" class="form-control" id="managerBirthPlace" name="birth_place">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ الميلاد</label>
                                        <input type="date" class="form-control" id="managerBirthDate" name="birth_date">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">العنوان الحالي</label>
                                        <textarea class="form-control" id="managerCurrentAddress" name="current_address"></textarea>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">أرقام الاتصال</label>
                                        <input type="text" class="form-control" id="managerContactNumbers" name="contact_numbers">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" id="managerEmail" name="email">
                                    </div>

                                    <!-- المؤهلات العلمية -->
                                    <h4 class="mb-3">المؤهلات العلمية</h4>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الدرجة العلمية</label>
                                        <select class="form-select" id="managerEducationDegree" name="education_degree">
                                            <option value="">اختر الدرجة العلمية</option>
                                            <option value="دكتوراه">دكتوراه</option>
                                            <option value="ماجستير">ماجستير</option>
                                            <option value="بكالوريوس">بكالوريوس</option>
                                            <option value="دبلوم">دبلوم</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">التخصص</label>
                                        <input type="text" class="form-control" id="managerSpecialization" name="specialization">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ الحصول على المؤهل</label>
                                        <input type="date" class="form-control" id="managerEducationDate" name="education_date">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الشهادات الإضافية</label>
                                        <textarea class="form-control" id="managerCertificates" name="certificates"></textarea>
                                    </div>

                                    <!-- بيانات الوظيفة -->
                                    <h4 class="mb-3">بيانات الوظيفة</h4>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">المسمى الحالي</label>
                                        <input type="text" class="form-control" id="managerCurrentTitle" name="current_title" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">المسمى الوظيفي</label>
                                        <input type="text" class="form-control" id="managerJobTitle" name="job_title">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">المرتبة الوظيفية</label>
                                        <input type="text" class="form-control" id="managerJobLevel" name="job_level">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ التعيين</label>
                                        <input type="date" class="form-control" id="managerEmploymentDate" name="employment_date">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">جهة العمل الرئيسية</label>
                                        <input type="text" class="form-control" id="managerMainWorkplace" name="main_workplace">
                                    </div>

                                    <!-- بيانات قرار التعيين -->
                                    <h4 class="mb-3">بيانات قرار التعيين</h4>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">نوع التعيين</label>
                                        <select class="form-select" id="managerAppointmentType" name="appointment_type">
                                            <option value="">اختر نوع التعيين</option>
                                            <option value="دائم">دائم</option>
                                            <option value="مؤقت">مؤقت</option>
                                            <option value="تعاقد">تعاقد</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تفاصيل نوع التعيين</label>
                                        <input type="text" class="form-control" id="managerAppointmentTypeDetail" name="appointment_type_detail">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رقم قرار التعيين</label>
                                        <input type="text" class="form-control" id="managerAppointmentDecisionNumber" name="appointment_decision_number">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ قرار التعيين</label>
                                        <input type="date" class="form-control" id="managerAppointmentDecisionDate" name="appointment_decision_date">
                                    </div>
                                    <div class="col-md-12 mb-3">
                                        <label class="form-label">عنوان مقر العمل</label>
                                        <textarea class="form-control" id="managerWorkplaceAddress" name="workplace_address"></textarea>
                                    </div>
                                </div>

                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                    <button type="submit" class="btn btn-primary">حفظ</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قسم البحث والفلترة -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-4 mb-2">
                            <input type="text" class="form-control" id="searchInput" placeholder="بحث...">
                        </div>
                        <div class="col-md-3 mb-2">
                            <select class="form-select" id="departmentFilter">
                                <option value="">كل الأقسام</option>
                            </select>
                        </div>
                        <div class="col-md-3 mb-2">
                            <select class="form-select" id="educationFilter">
                                <option value="">كل المؤهلات</option>
                                <option value="دكتوراه">دكتوراه</option>
                                <option value="ماجستير">ماجستير</option>
                                <option value="بكالوريوس">بكالوريوس</option>
                                <option value="دبلوم">دبلوم</option>
                                <option value="ثانوي">ثانوي</option>
                                <option value="أساسي">أساسي</option>
                            </select>
                        </div>
                        <div class="col-md-2 mb-2">
                            <button class="btn btn-primary w-100" onclick="printManagersData()">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول المدراء -->
            <div class="table-responsive">
                <table id="managersTable" class="table table-striped">
                    <thead>
                        <tr>
                            <th>الاسم الكامل</th>
                            <th>المسمى الحالي</th>
                            <th>القسم</th>
                            <th>المؤهل العلمي</th>
                            <th>رقم الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>تاريخ التعيين</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم ملء هذا الجزء ديناميكياً -->
                    </tbody>
                </table>
            </div>

            <!-- Modal نموذج استيراد المدراء من Excel -->
            <div class="modal fade" id="importManagersModal" tabindex="-1" data-bs-backdrop="static">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">استيراد بيانات المدراء من ملف Excel</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                يرجى تحميل ملف Excel يحتوي على بيانات المدراء. يجب أن يحتوي الملف على الأعمدة التالية: الاسم الكامل، المسمى الحالي، القسم، المؤهل العلمي، رقم الهاتف، البريد الإلكتروني، تاريخ التعيين.
                            </div>
                            <form id="importManagersForm">
                                <div class="mb-3">
                                    <label class="form-label">اختر ملف Excel</label>
                                    <input type="file" class="form-control" id="excelFileInput" accept=".xlsx, .xls" required>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="importManagersFromExcel()">استيراد</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal نموذج عرض تفاصيل المدير -->
            <div class="modal fade" id="managerDetailsModal" tabindex="-1" data-bs-backdrop="static">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل المدير</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div id="managerDetailsContent">
                                <!-- سيتم ملء هذا الجزء ديناميكياً -->
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="printManagerDetails()">
                                <i class="fas fa-print me-2"></i>طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal نموذج تعديل بيانات المدير -->
            <div class="modal fade" id="editManagerModal" tabindex="-1" data-bs-backdrop="static">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تعديل بيانات المدير</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="editManagerForm">
                                <input type="hidden" id="editManagerId" name="id">
                                <div class="row">
                                    <!-- البيانات الشخصية -->
                                    <h4 class="mb-3">البيانات الشخصية</h4>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الاسم الكامل</label>
                                        <input type="text" class="form-control" id="editManagerFullName" name="full_name" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">مكان الميلاد</label>
                                        <input type="text" class="form-control" id="editManagerBirthPlace" name="birth_place">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ الميلاد</label>
                                        <input type="date" class="form-control" id="editManagerBirthDate" name="birth_date">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">العنوان الحالي</label>
                                        <textarea class="form-control" id="editManagerCurrentAddress" name="current_address"></textarea>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">أرقام الاتصال</label>
                                        <input type="text" class="form-control" id="editManagerContactNumbers" name="contact_numbers">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" id="editManagerEmail" name="email">
                                    </div>

                                    <!-- المؤهلات العلمية -->
                                    <h4 class="mb-3">المؤهلات العلمية</h4>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الدرجة العلمية</label>
                                        <select class="form-select" id="editManagerEducationDegree" name="education_degree">
                                            <option value="">اختر الدرجة العلمية</option>
                                            <option value="دكتوراه">دكتوراه</option>
                                            <option value="ماجستير">ماجستير</option>
                                            <option value="بكالوريوس">بكالوريوس</option>
                                            <option value="دبلوم">دبلوم</option>
                                            <option value="ثانوي">ثانوي</option>
                                            <option value="أساسي">أساسي</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">التخصص</label>
                                        <input type="text" class="form-control" id="editManagerSpecialization" name="specialization">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ الحصول على المؤهل</label>
                                        <input type="date" class="form-control" id="editManagerEducationDate" name="education_date">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الشهادات الإضافية</label>
                                        <textarea class="form-control" id="editManagerCertificates" name="certificates"></textarea>
                                    </div>

                                    <!-- بيانات الوظيفة -->
                                    <h4 class="mb-3">بيانات الوظيفة</h4>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">المسمى الحالي</label>
                                        <input type="text" class="form-control" id="editManagerCurrentTitle" name="current_title" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">المسمى الوظيفي</label>
                                        <input type="text" class="form-control" id="editManagerJobTitle" name="job_title">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">المرتبة الوظيفية</label>
                                        <input type="text" class="form-control" id="editManagerJobLevel" name="job_level">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ التعيين</label>
                                        <input type="date" class="form-control" id="editManagerEmploymentDate" name="employment_date">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">جهة العمل الرئيسية</label>
                                        <input type="text" class="form-control" id="editManagerMainWorkplace" name="main_workplace">
                                    </div>

                                    <!-- بيانات قرار التعيين -->
                                    <h4 class="mb-3">بيانات قرار التعيين</h4>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">نوع التعيين</label>
                                        <select class="form-select" id="editManagerAppointmentType" name="appointment_type">
                                            <option value="">اختر نوع التعيين</option>
                                            <option value="دائم">دائم</option>
                                            <option value="مؤقت">مؤقت</option>
                                            <option value="تعاقد">تعاقد</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تفاصيل نوع التعيين</label>
                                        <input type="text" class="form-control" id="editManagerAppointmentTypeDetail" name="appointment_type_detail">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رقم قرار التعيين</label>
                                        <input type="text" class="form-control" id="editManagerAppointmentDecisionNumber" name="appointment_decision_number">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ قرار التعيين</label>
                                        <input type="date" class="form-control" id="editManagerAppointmentDecisionDate" name="appointment_decision_date">
                                    </div>
                                    <div class="col-md-12 mb-3">
                                        <label class="form-label">عنوان مقر العمل</label>
                                        <textarea class="form-control" id="editManagerWorkplaceAddress" name="workplace_address"></textarea>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="updateManager()">حفظ التغييرات</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal نموذج تأكيد حذف المدير -->
            <div class="modal fade" id="deleteManagerModal" tabindex="-1" data-bs-backdrop="static">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تأكيد الحذف</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>هل أنت متأكد من حذف بيانات المدير <span id="deleteManagerName" class="fw-bold"></span>؟</p>
                            <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
                            <input type="hidden" id="deleteManagerId">
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-danger" onclick="confirmDeleteManager()">
                                <i class="fas fa-trash me-2"></i>تأكيد الحذف
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal نموذج عرض تفاصيل الطلب -->
            <div class="modal fade" id="requestDetailsModal" tabindex="-1" data-bs-backdrop="static">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تفاصيل الطلب</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div id="requestDetailsContent">
                                <!-- سيتم ملء هذا الجزء ديناميكياً -->
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="printRequestDetails()">
                                <i class="fas fa-print me-2"></i>طباعة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Modal نموذج طلب سفر -->
            <div class="modal fade" id="travelFormModal" tabindex="-1" data-bs-backdrop="static">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">نموذج طلب سفر جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div id="travelFormContainer">
                                <!-- سيتم نسخ محتوى نموذج طلب السفر هنا -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Modal نموذج طلب إجازة -->
            <div class="modal fade" id="vacationFormModal" tabindex="-1" data-bs-backdrop="static">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">نموذج طلب إجازة جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div id="vacationFormContainer">
                                <!-- سيتم نسخ محتوى نموذج طلب الإجازة هنا -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal نموذج تأكيد حذف الطلب -->
            <div class="modal fade" id="deleteRequestModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تأكيد الحذف</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>هل أنت متأكد من حذف الطلب رقم <span id="deleteRequestId" class="fw-bold"></span>؟</p>
                            <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
                            <input type="hidden" id="deleteRequestIdInput">
                            <input type="hidden" id="deleteRequestTypeInput">
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" id="deleteConfirmButton" class="btn btn-danger" onclick="confirmDeleteRequest()">
                                <i class="fas fa-trash me-2"></i>تأكيد الحذف
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Requests Entry Section -->
        <div id="requestsEntrySection" class="content-section" style="display: none;">
            <div class="section-header mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h3 class="section-title">
                            <i class="fas fa-file-circle-plus me-2"></i>واجهة إدخال الطلبات
                        </h3>
                    </div>
                </div>
            </div>

            <!-- أزرار إنشاء طلب جديد -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">إنشاء طلب جديد</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary" onclick="showTravelFormModal()">
                                    <i class="fas fa-plane-departure me-2"></i>طلب سفر جديد
                                </button>
                                <button class="btn btn-success" onclick="showVacationFormModal()">
                                    <i class="fas fa-calendar-alt me-2"></i>طلب إجازة جديد
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Requests Table -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>سجل الطلبات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="requestsEntryTable">
                            <thead>
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>نوع الطلب</th>
                                    <th>اسم المدير</th>
                                    <th>المنصب</th>
                                    <th>تاريخ التقديم</th>
                                    <th>تاريخ البداية</th>
                                    <th>تاريخ النهاية</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- سيتم ملء هذا الجزء ديناميكياً -->
                                <tr>
                                    <td colspan="8" class="text-center">لا توجد طلبات مسجلة</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reports Section -->
        <div id="reportsSection" class="content-section" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>التقارير والإحصائيات</h5>
                </div>
                <div class="card-body">
                    <!-- Report Filters -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-filter me-2"></i>خيارات الفلترة</h6>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">من تاريخ</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                        <input type="date" id="reportStartDate" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">إلى تاريخ</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                        <input type="date" id="reportEndDate" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">نوع الطلب</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-file-alt"></i></span>
                                        <select id="reportRequestType" class="form-select">
                                            <option value="">الكل</option>
                                            <option value="travel">طلبات السفر</option>
                                            <option value="vacation">طلبات الإجازات</option>
                                        </select>
                                    </div>
                                </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">المدير</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <select id="reportManagerFilter" class="form-select">
                                            <option value="">كل المدراء</option>
                                            <!-- سيتم تعبئة هذه القائمة ديناميكياً -->
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">القسم</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-building"></i></span>
                                        <select id="reportDepartmentFilter" class="form-select">
                                            <option value="">كل الأقسام</option>
                                            <!-- سيتم تعبئة هذه القائمة ديناميكياً -->
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">بحث</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                        <input type="text" id="reportSearchInput" class="form-control" placeholder="بحث...">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <button id="filterReportsBtn" class="btn btn-primary" onclick="filterReports(); return false;">
                                <i class="fas fa-search me-2"></i>عرض النتائج
                            </button>
                            <button id="resetFiltersBtn" class="btn btn-outline-secondary ms-2" onclick="resetReportFilters(); return false;">
                                <i class="fas fa-redo me-2"></i>إعادة تعيين
                            </button>
                        </div>
                    </div>

                    <!-- Report Actions -->
                    <div class="d-flex gap-2 mb-4">
                        <button id="exportExcelBtn" class="btn btn-success" onclick="exportReportsToExcel(); return false;">
                            <i class="fas fa-file-excel me-2"></i>تصدير Excel
                        </button>
                        <button id="exportPdfBtn" class="btn btn-danger" onclick="exportReportsToPdf(); return false;">
                            <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                        </button>
                    </div>

                    <!-- Report Results -->
                    <div class="table-responsive">
                        <table id="reportsTable" class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الطلب</th>
                                    <th>نوع الطلب</th>
                                    <th>مقدم الطلب</th>
                                    <th>المنصب</th>
                                    <th>تاريخ التقديم</th>
                                    <th>تاريخ البداية</th>
                                    <th>تاريخ النهاية</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="8" class="text-center">يرجى تحديد معايير البحث والضغط على زر "عرض النتائج"</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users Management Section -->
        <div id="usersManagementSection" class="content-section" style="display: none;">
            <div class="section-header mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <h3 class="section-title">
                            <i class="fas fa-users-cog me-2"></i>إدارة المستخدمين
                        </h3>
                    </div>
                </div>
            </div>

            <!-- أزرار التحكم -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">إدارة المستخدمين</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <button id="addUserBtn" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#userFormModal">
                                    <i class="fas fa-plus-circle me-2"></i>إضافة مستخدم جديد
                                </button>
                                <div class="input-group w-50">
                                    <input type="text" id="searchUserInput" class="form-control" placeholder="بحث عن مستخدم...">
                                    <button class="btn btn-outline-secondary" type="button" id="searchUserBtn">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول المستخدمين -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-users me-2"></i>قائمة المستخدمين</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="usersTable">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>اسم المستخدم</th>
                                    <th>الاسم الكامل</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الصلاحية</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="usersTableBody">
                                <tr>
                                    <td colspan="8" class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                        <p class="mt-2">جاري تحميل بيانات المستخدمين...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <span id="totalUsers">إجمالي المستخدمين: <span class="badge bg-primary">0</span></span>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-secondary" id="refreshUsersBtn">
                                <i class="fas fa-sync-alt me-1"></i>تحديث
                            </button>
                            <button class="btn btn-sm btn-outline-primary" id="exportUsersBtn">
                                <i class="fas fa-file-export me-1"></i>تصدير
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج إضافة/تعديل مستخدم -->
            <div class="modal fade" id="userFormModal" tabindex="-1" aria-labelledby="userFormModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="userFormModalLabel">إضافة مستخدم جديد</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                        </div>
                        <div class="modal-body">
                            <form id="userForm" class="needs-validation" novalidate>
                                <input type="hidden" id="userId" name="id">

                                <div class="mb-3">
                                    <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        <input type="text" class="form-control" id="username" name="username" required>
                                    </div>
                                    <div class="invalid-feedback">يرجى إدخال اسم المستخدم</div>
                                </div>

                                <div class="mb-3">
                                    <label for="fullName" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user-tag"></i></span>
                                        <input type="text" class="form-control" id="fullName" name="full_name" required>
                                    </div>
                                    <div class="invalid-feedback">يرجى إدخال الاسم الكامل</div>
                                </div>

                                <div class="mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                        <input type="email" class="form-control" id="email" name="email">
                                    </div>
                                    <div class="invalid-feedback">يرجى إدخال بريد إلكتروني صحيح</div>
                                </div>

                                <div class="mb-3">
                                    <label for="password" class="form-label">كلمة المرور <span class="text-danger password-required">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                        <input type="password" class="form-control" id="password" name="password" required>
                                        <button class="btn btn-outline-secondary toggle-password" type="button">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">يرجى إدخال كلمة مرور</div>
                                    <div class="form-text">يجب أن تحتوي على 4 أحرف على الأقل</div>
                                </div>

                                <div class="mb-3">
                                    <label for="role" class="form-label">الصلاحية <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-user-shield"></i></span>
                                        <select class="form-select" id="role" name="role" required>
                                            <option value="">اختر الصلاحية...</option>
                                            <option value="admin">مدير النظام</option>
                                            <option value="editor">محرر</option>
                                            <option value="viewer">مشاهد</option>
                                            <option value="user">مستخدم عادي</option>
                                        </select>
                                    </div>
                                    <div class="invalid-feedback">يرجى اختيار الصلاحية</div>
                                </div>

                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="isActive" name="is_active" checked>
                                    <label class="form-check-label" for="isActive">مستخدم نشط</label>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" id="saveUserBtn">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نافذة تأكيد الحذف -->
            <div class="modal fade" id="deleteUserModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-sm">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">تأكيد الحذف</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                        </div>
                        <div class="modal-body">
                            <p>هل أنت متأكد من رغبتك في حذف هذا المستخدم؟</p>
                            <p class="text-danger"><small>لا يمكن التراجع عن هذا الإجراء.</small></p>
                            <input type="hidden" id="deleteUserId">
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-danger" id="confirmDeleteUserBtn">حذف</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted"> جميع الحقوق محفوظة  للأدارة العامة لمكتب محافظ محافظة حضرموت  2025</span>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <script>
        // تبسيط الكود ليعمل مباشرة
        document.addEventListener('DOMContentLoaded', function() {
            // تحديث عدادات الطلبات مباشرة عند تحميل الصفحة
            if (typeof updateRequestsCounters === 'function') {
                updateRequestsCounters();
            }

            // تحديث لوحات المعلومات في الصفحة الرئيسية
            if (typeof updateDashboardStats === 'function') {
                updateDashboardStats();
            }

            showSection('homeSection');
        });

        function showSection(sectionId) {
            document.querySelectorAll('.content-section').forEach(section => {
                section.style.display = 'none';
            });

            document.getElementById(sectionId).style.display = 'block';
            updateBreadcrumb(sectionId);
            updateActiveNavItem(sectionId);
        }

        function updateBreadcrumb(sectionId) {
            const sectionTitles = {
                'homeSection': 'الرئيسية',
                'travelFormSection': 'نموذج طلب سفر',
                'vacationFormSection': 'نموذج طلب إجازة',
                'managersSettingsSection': 'إدارة المدراء',
                'reportsSection': 'التقارير والإحصائيات',
                'usersManagementSection': 'إدارة المستخدمين'
            };

            document.getElementById('currentSection').textContent = sectionTitles[sectionId] || 'لوحة التحكم';
        }

        function updateActiveNavItem(sectionId) {
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            const activeLink = document.querySelector(`.nav-link[data-section="${sectionId}"]`);
            if (activeLink) {
                activeLink.classList.add('active');
            }
        }

        function toggleNotifications() {
            const menu = document.getElementById('notificationsMenu');
            menu.style.display = menu.style.display === 'none' ? 'block' : 'none';
        }

        function showTravelForm() {
            showSection('travelFormSection');
        }

        function showHome() {
            showSection('homeSection');
        }

        // إضافة معالجة النماذج
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                alert('تم إرسال النموذج بنجاح');
                this.reset();
            });
        });

        // تم نقل كود إدارة المدراء إلى ملف script.js
    </script>
    <!-- Modal تأكيد حذف الطلب -->
    <div class="modal fade" id="deleteRequestModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">تأكيد حذف الطلب</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف الطلب رقم <span id="deleteRequestId" class="fw-bold"></span>؟</p>
                    <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
                    <input type="hidden" id="deleteRequestIdInput">
                    <input type="hidden" id="deleteRequestTypeInput">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" id="deleteConfirmButton" class="btn btn-danger" onclick="confirmDeleteRequest()">
                        <i class="fas fa-trash me-2"></i>تأكيد الحذف
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تفاصيل الطلب -->
    <div class="modal fade" id="requestDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل الطلب</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="requestDetailsContent">
                        <!-- سيتم ملء هذا القسم ديناميكياً -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-info" onclick="printRequestDetails()">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تمديد طلب السفر -->
    <div class="modal fade" id="extendTravelModal" tabindex="-1" aria-labelledby="extendTravelModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="extendTravelModalLabel">تمديد طلب السفر</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <form id="extendTravelForm">
                        <input type="hidden" id="extendTravelRequestId" name="requestId">
                        <div class="mb-3">
                            <label for="newTravelEndDate" class="form-label">تاريخ العودة الجديد</label>
                            <input type="date" class="form-control" id="newTravelEndDate" name="newTravelEndDate" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="submitTravelExtension()">تمديد</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تمديد طلب الإجازة -->
    <div class="modal fade" id="extendVacationModal" tabindex="-1" aria-labelledby="extendVacationModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="extendVacationModalLabel">تمديد طلب الإجازة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <form id="extendVacationForm">
                        <input type="hidden" id="extendVacationRequestId" name="requestId">
                        <div class="mb-3">
                            <label for="newVacationEndDate" class="form-label">تاريخ الانتهاء الجديد</label>
                            <input type="date" class="form-control" id="newVacationEndDate" name="newVacationEndDate" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="submitVacationExtension()">تمديد</button>
                </div>
            </div>
        </div>
    </div>

    <!-- مكتبة Chart.js للرسوم البيانية -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script src="countries.js"></script>
    <script>
        // التحقق من حالة تسجيل الدخول
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('authToken');
            if (!token) {
                // إذا لم يكن المستخدم مسجل الدخول، قم بتوجيهه إلى صفحة تسجيل الدخول
                window.location.href = 'login.html';
            } else {
                // التحقق مما إذا كان هذا تسجيل دخول مباشر للتجربة
                if (token === 'mock_token_for_testing') {
                    console.log('تم التعرف على تسجيل الدخول المباشر');

                    // تحديث معلومات المستخدم في الواجهة
                    const user = JSON.parse(localStorage.getItem('user'));
                    if (user) {
                        const userInfoElement = document.getElementById('userInfo');
                        if (userInfoElement) {
                            userInfoElement.textContent = user.full_name || user.username;
                        }
                    }
                    return;
                }

                // التحقق من صحة رمز المصادقة
                fetch('/api/verify-token', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + token
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('رمز المصادقة غير صالح');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.status !== 'success') {
                        throw new Error('رمز المصادقة غير صالح');
                    }

                    // تحديث معلومات المستخدم في الواجهة
                    const user = JSON.parse(localStorage.getItem('user'));
                    if (user) {
                        const userInfoElement = document.getElementById('userInfo');
                        if (userInfoElement) {
                            userInfoElement.textContent = user.full_name || user.username;
                        }
                    }
                })
                .catch(error => {
                    console.error('خطأ في التحقق من رمز المصادقة:', error);

                    // للتجربة فقط: إذا كان الخادم غير متاح، استمر في استخدام الصفحة
                    console.log('استمرار استخدام الصفحة بدون التحقق من الخادم');

                    // تحديث معلومات المستخدم في الواجهة
                    const user = JSON.parse(localStorage.getItem('user'));
                    if (user) {
                        const userInfoElement = document.getElementById('userInfo');
                        if (userInfoElement) {
                            userInfoElement.textContent = user.full_name || user.username;
                        }
                    }

                    // حذف رمز المصادقة ومعلومات المستخدم من التخزين المحلي فقط إذا كان الخطأ ليس بسبب عدم توفر الخادم
                    if (!error.message.includes('Failed to fetch')) {
                        localStorage.removeItem('authToken');
                        localStorage.removeItem('user');
                        // توجيه المستخدم إلى صفحة تسجيل الدخول
                        window.location.href = 'login.html';
                    }
                });
            }
        });

        // دالة لتسجيل الخروج
        function logout() {
            // حذف رمز المصادقة ومعلومات المستخدم من التخزين المحلي
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
            // توجيه المستخدم إلى صفحة تسجيل الدخول
            window.location.href = 'login.html';
        }
    </script>
    <!-- مكتبات خارجية -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>

    <!-- ملفات JavaScript الخاصة بالتطبيق -->
    <script src="api-client.js"></script>
    <script src="showAlert.js"></script>
    <script src="export-pdf.js"></script>
    <script src="export-excel.js"></script>
    <script src="request-actions.js"></script>
    <script src="script.js"></script>
    <script src="print-form.js"></script>
    <script src="users-admin.js"></script>

</body>
</html>

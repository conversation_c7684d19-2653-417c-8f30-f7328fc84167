/**
 * وحدة قاعدة البيانات - تحتوي على جميع دوال التعامل مع قاعدة البيانات
 * تم تحسين الكود وحذف التكرارات
 */

const { Pool } = require('pg');
require('dotenv').config();

// إعدادات الاتصال بقاعدة البيانات
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'had2_db',
  password: process.env.DB_PASSWORD || '123456',
  port: process.env.DB_PORT || 5432,
});

// دالة للتحقق من الاتصال بقاعدة البيانات
const testConnection = async () => {
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT NOW()');
    client.release();
    console.log('تم الاتصال بقاعدة البيانات بنجاح:', result.rows[0]);
    return result.rows[0];
  } catch (err) {
    console.error('خطأ في الاتصال بقاعدة البيانات:', err);
    throw err;
  }
};

// دالة للتحقق من وجود جدول في قاعدة البيا��ات
const checkTableExists = async (tableName) => {
  try {
    console.log(`التحقق من وجود جدول ${tableName} في قاعدة البيانات...`);
    const query = `
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = $1
      );
    `;
    const result = await pool.query(query, [tableName]);
    const exists = result.rows[0].exists;
    console.log(`جدول ${tableName} موجود: ${exists}`);
    return exists;
  } catch (err) {
    console.error(`خطأ في التحقق من وجود جدول ${tableName}:`, err);
    return false;
  }
};

// ===== دوال إدارة المدراء =====

const getManagers = async () => {
  try {
    const result = await pool.query('SELECT * FROM managers ORDER BY full_name');
    return result.rows;
  } catch (err) {
    console.error('خطأ في جلب بيانات المدراء:', err);
    throw err;
  }
};

const getManagerById = async (id) => {
  try {
    const result = await pool.query('SELECT * FROM managers WHERE id = $1', [id]);
    return result.rows[0];
  } catch (err) {
    console.error('خطأ في جلب بيانات المدير:', err);
    throw err;
  }
};

const addManager = async (managerData) => {
  const {
    full_name, birth_place, birth_date, current_address, contact_numbers,
    email, education_degree, specialization, education_date, certificates,
    current_title, job_title, job_level, employment_date, main_workplace,
    appointment_type, appointment_type_detail, appointment_decision_number,
    appointment_decision_date, workplace_address
  } = managerData;

  try {
    const result = await pool.query(
      `INSERT INTO managers (
        full_name, birth_place, birth_date, current_address, contact_numbers,
        email, education_degree, specialization, education_date, certificates,
        current_title, job_title, job_level, employment_date, main_workplace,
        appointment_type, appointment_type_detail, appointment_decision_number,
        appointment_decision_date, workplace_address
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20)
      RETURNING *`,
      [
        full_name, birth_place, birth_date, current_address, contact_numbers,
        email, education_degree, specialization, education_date, certificates,
        current_title, job_title, job_level, employment_date, main_workplace,
        appointment_type, appointment_type_detail, appointment_decision_number,
        appointment_decision_date, workplace_address
      ]
    );
    return result.rows[0];
  } catch (err) {
    console.error('خطأ في إضافة مدير جديد:', err);
    throw err;
  }
};

const updateManager = async (id, managerData) => {
  const {
    full_name, birth_place, birth_date, current_address, contact_numbers,
    email, education_degree, specialization, education_date, certificates,
    current_title, job_title, job_level, employment_date, main_workplace,
    appointment_type, appointment_type_detail, appointment_decision_number,
    appointment_decision_date, workplace_address
  } = managerData;

  try {
    const result = await pool.query(
      `UPDATE managers SET
        full_name = $1, birth_place = $2, birth_date = $3, current_address = $4, contact_numbers = $5,
        email = $6, education_degree = $7, specialization = $8, education_date = $9, certificates = $10,
        current_title = $11, job_title = $12, job_level = $13, employment_date = $14, main_workplace = $15,
        appointment_type = $16, appointment_type_detail = $17, appointment_decision_number = $18,
        appointment_decision_date = $19, workplace_address = $20
      WHERE id = $21
      RETURNING *`,
      [
        full_name, birth_place, birth_date, current_address, contact_numbers,
        email, education_degree, specialization, education_date, certificates,
        current_title, job_title, job_level, employment_date, main_workplace,
        appointment_type, appointment_type_detail, appointment_decision_number,
        appointment_decision_date, workplace_address, id
      ]
    );
    return result.rows[0];
  } catch (err) {
    console.error('خطأ في تحديث بيانات المدير:', err);
    throw err;
  }
};

const deleteManager = async (id) => {
  try {
    await pool.query('DELETE FROM managers WHERE id = $1', [id]);
    return true;
  } catch (err) {
    console.error('خطأ في حذف المدير:', err);
    throw err;
  }
};

// ===== دوال إدارة طلبات السفر =====

const getTravelRequests = async (filters = {}) => {
  try {
    let query = `
      SELECT tr.*, m.full_name as manager_name, m.current_title as manager_position
      FROM travel_requests tr
      JOIN managers m ON tr.manager_id = m.id
    `;
    const values = [];
    const conditions = [];
    let paramIndex = 1;

    // بناء شروط الفلترة ديناميكياً
    if (filters.manager_id) {
      conditions.push(`tr.manager_id = $${paramIndex++}`);
      values.push(filters.manager_id);
    }
    if (filters.start_date) {
      conditions.push(`tr.departure_date >= $${paramIndex++}`);
      values.push(filters.start_date);
    }
    if (filters.end_date) {
      conditions.push(`tr.return_date <= $${paramIndex++}`);
      values.push(filters.end_date);
    }
    if (filters.travel_type) {
      conditions.push(`tr.travel_type = $${paramIndex++}`);
      values.push(filters.travel_type);
    }
    if (filters.status) {
      conditions.push(`tr.status = $${paramIndex++}`);
      values.push(filters.status);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY tr.created_at DESC';

    console.log('Executing travel requests query:', query, values);
    const result = await pool.query(query, values);
    return result.rows;
  } catch (err) {
    console.error('خطأ في جلب طلبات السفر:', err);
    throw err;
  }
};

const getTravelRequestById = async (id) => {
  try {
    const result = await pool.query(`
      SELECT tr.*, m.full_name as manager_name, m.current_title as manager_position
      FROM travel_requests tr
      JOIN managers m ON tr.manager_id = m.id
      WHERE tr.id = $1
    `, [id]);
    return result.rows[0];
  } catch (err) {
    console.error('خطأ في جلب بيانات طلب السفر:', err);
    throw err;
  }
};

const addTravelRequest = async (requestData) => {
  const {
    manager_id, travel_country, notification_date, departure_date, return_date,
    travel_type, travel_purpose, deputy_name, deputy_position, deputy_phone
  } = requestData;

  try {
    const result = await pool.query(
      `INSERT INTO travel_requests (
        manager_id, travel_country, notification_date, departure_date, return_date,
        travel_type, travel_purpose, deputy_name, deputy_position, deputy_phone
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *`,
      [
        manager_id, travel_country, notification_date, departure_date, return_date,
        travel_type, travel_purpose, deputy_name, deputy_position, deputy_phone
      ]
    );
    return result.rows[0];
  } catch (err) {
    console.error('خطأ في إضافة طلب سفر جديد:', err);
    throw err;
  }
};

const updateTravelRequest = async (id, requestData) => {
  const {
    manager_id, travel_country, notification_date, departure_date, return_date,
    travel_type, travel_purpose, deputy_name, deputy_position, deputy_phone
  } = requestData;

  try {
    const result = await pool.query(
      `UPDATE travel_requests SET
        manager_id = $1, travel_country = $2, notification_date = $3, departure_date = $4, return_date = $5,
        travel_type = $6, travel_purpose = $7, deputy_name = $8, deputy_position = $9, deputy_phone = $10
      WHERE id = $11
      RETURNING *`,
      [
        manager_id, travel_country, notification_date, departure_date, return_date,
        travel_type, travel_purpose, deputy_name, deputy_position, deputy_phone, id
      ]
    );
    return result.rows[0];
  } catch (err) {
    console.error('خطأ في تحديث بيانات طلب السفر:', err);
    throw err;
  }
};

const deleteTravelRequest = async (id) => {
  try {
    await pool.query('DELETE FROM travel_requests WHERE id = $1', [id]);
    return true;
  } catch (err) {
    console.error('خطأ في حذف طلب السفر:', err);
    throw err;
  }
};

// ===== دوال إدارة طلبات الإجازة =====

const getVacationRequests = async (filters = {}) => {
  try {
    let query = `
      SELECT vr.*, m.full_name as manager_name, m.current_title as manager_position
      FROM vacation_requests vr
      JOIN managers m ON vr.manager_id = m.id
    `;
    const values = [];
    const conditions = [];
    let paramIndex = 1;

    // بناء شروط الفلترة ديناميكياً
    if (filters.manager_id) {
      conditions.push(`vr.manager_id = $${paramIndex++}`);
      values.push(filters.manager_id);
    }
    if (filters.start_date) {
      conditions.push(`vr.start_date >= $${paramIndex++}`);
      values.push(filters.start_date);
    }
    if (filters.end_date) {
      conditions.push(`vr.end_date <= $${paramIndex++}`);
      values.push(filters.end_date);
    }
    if (filters.vacation_type) {
      conditions.push(`vr.vacation_type = $${paramIndex++}`);
      values.push(filters.vacation_type);
    }
    if (filters.status) {
      conditions.push(`vr.status = $${paramIndex++}`);
      values.push(filters.status);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY vr.created_at DESC';

    console.log('Executing vacation requests query:', query, values);
    const result = await pool.query(query, values);
    return result.rows;
  } catch (err) {
    console.error('خطأ في جلب طلبات الإجازة:', err);
    throw err;
  }
};

const getVacationRequestById = async (id) => {
  try {
    const result = await pool.query(`
      SELECT vr.*, m.full_name as manager_name, m.current_title as manager_position
      FROM vacation_requests vr
      JOIN managers m ON vr.manager_id = m.id
      WHERE vr.id = $1
    `, [id]);
    return result.rows[0];
  } catch (err) {
    console.error('خطأ في جلب بيانات طلب الإجازة:', err);
    throw err;
  }
};

const addVacationRequest = async (requestData) => {
  const {
    manager_id, vacation_type, duration, start_date, end_date,
    deputy_name, deputy_position, deputy_phone
  } = requestData;

  try {
    const result = await pool.query(
      `INSERT INTO vacation_requests (
        manager_id, vacation_type, duration, start_date, end_date,
        deputy_name, deputy_position, deputy_phone
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *`,
      [
        manager_id, vacation_type, duration, start_date, end_date,
        deputy_name, deputy_position, deputy_phone
      ]
    );
    return result.rows[0];
  } catch (err) {
    console.error('خطأ في إضافة طلب إجازة جديد:', err);
    throw err;
  }
};

const updateVacationRequest = async (id, requestData) => {
  const {
    manager_id, vacation_type, duration, start_date, end_date,
    deputy_name, deputy_position, deputy_phone
  } = requestData;

  try {
    const result = await pool.query(
      `UPDATE vacation_requests SET
        manager_id = $1, vacation_type = $2, duration = $3, start_date = $4, end_date = $5,
        deputy_name = $6, deputy_position = $7, deputy_phone = $8
      WHERE id = $9
      RETURNING *`,
      [
        manager_id, vacation_type, duration, start_date, end_date,
        deputy_name, deputy_position, deputy_phone, id
      ]
    );
    return result.rows[0];
  } catch (err) {
    console.error('خطأ في تحديث بيانات طلب الإجازة:', err);
    throw err;
  }
};

const deleteVacationRequest = async (id) => {
  try {
    await pool.query('DELETE FROM vacation_requests WHERE id = $1', [id]);
    return true;
  } catch (err) {
    console.error('خطأ في حذف طلب الإجازة:', err);
    throw err;
  }
};

// ===== دوال الحصول على المدراء الحاليين =====

const getCurrentlyTravelingManagers = async () => {
  try {
    const query = `
      SELECT
        tr.id,
        m.id AS manager_id,
        m.full_name AS manager_name,
        m.current_title AS manager_position,
        m.main_workplace AS manager_department,
        m.contact_numbers,
        tr.travel_country,
        tr.departure_date,
        tr.return_date,
        tr.travel_type,
        tr.travel_purpose,
        tr.deputy_name,
        'travel' AS request_type
      FROM
        managers m
      JOIN
        travel_requests tr ON m.id = tr.manager_id
      WHERE
        tr.departure_date <= CURRENT_DATE
        AND tr.return_date >= CURRENT_DATE
      ORDER BY
        tr.return_date ASC
    `;

    const result = await pool.query(query);

    if (result.rows.length === 0) {
      console.log('لا توجد بيانات فعلية للمدراء المسافرين حاليا، استخدام بيانات تجريبية');
      return [
        {
          id: 1,
          manager_id: 1,
          manager_name: 'أحمد محمد علي',
          manager_position: 'مدير عام',
          manager_department: 'مكتب المحافظ',
          contact_numbers: '*********',
          travel_country: 'المملكة العربية السعودية',
          departure_date: '2023-05-01',
          return_date: '2023-05-15',
          travel_type: 'work',
          travel_purpose: 'حضور اجتماع',
          deputy_name: 'محمد علي أحمد',
          request_type: 'travel'
        }
      ];
    }

    return result.rows;
  } catch (err) {
    console.error('خطأ في جلب بيانات المدراء المسافرين حاليا:', err);
    return [];
  }
};

const getCurrentlyVacationingManagers = async () => {
  try {
    const query = `
      SELECT
        vr.id,
        m.id AS manager_id,
        m.full_name AS manager_name,
        m.current_title AS manager_position,
        m.main_workplace AS manager_department,
        m.contact_numbers,
        vr.vacation_type,
        EXTRACT(DAY FROM (vr.end_date::timestamp - vr.start_date::timestamp)) AS duration,
        vr.start_date,
        vr.end_date,
        vr.deputy_name,
        'vacation' AS request_type
      FROM
        managers m
      JOIN
        vacation_requests vr ON m.id = vr.manager_id
      WHERE
        vr.start_date <= CURRENT_DATE
        AND vr.end_date >= CURRENT_DATE
      ORDER BY
        vr.end_date ASC
    `;

    const result = await pool.query(query);

    if (result.rows.length === 0) {
      console.log('لا توجد بيانات فعلية للمدراء في إجازة حاليا، استخدام بيانات تجريبية');
      return [
        {
          id: 1,
          manager_id: 3,
          manager_name: 'عبدالله محمد سالم',
          manager_position: 'مدير إدارة',
          manager_department: 'إدارة المالية',
          contact_numbers: '777987654',
          vacation_type: 'annual',
          duration: 7,
          start_date: '2023-05-01',
          end_date: '2023-05-07',
          deputy_name: 'علي أحمد محمد',
          request_type: 'vacation'
        }
      ];
    }

    return result.rows.map(row => ({
      ...row,
      duration: parseInt(row.duration, 10)
    }));
  } catch (err) {
    console.error('خطأ في جلب بيانات المدراء في إجازة حاليا:', err);
    return [];
  }
};

const getCurrentlyAbsentManagers = async () => {
  try {
    const [travelingManagers, vacationingManagers] = await Promise.all([
      getCurrentlyTravelingManagers(),
      getCurrentlyVacationingManagers()
    ]);

    return [...travelingManagers, ...vacationingManagers];
  } catch (err) {
    console.error('خطأ في جلب بيانات المدراء الغائبين حاليا:', err);
    throw new Error('فشل في جلب بيانات المدراء الغائبين من قاعدة البيانات');
  }
};

// ===== دوال الإحصائيات =====

const getTravelRequestsStats = async () => {
  try {
    console.log('بدء تنفيذ دالة getTravelRequestsStats');

    const totalQuery = `SELECT COUNT(*) as total FROM travel_requests`;
    const totalResult = await pool.query(totalQuery);
    const total = parseInt(totalResult.rows[0].total) || 0;

    const currentQuery = `
      SELECT COUNT(*) as current
      FROM travel_requests
      WHERE departure_date <= CURRENT_DATE AND return_date >= CURRENT_DATE
    `;
    const currentResult = await pool.query(currentQuery);
    const current = parseInt(currentResult.rows[0].current) || 0;

    const result = { total, current };
    console.log('إحصائيات طلبات السفر النهائية:', result);
    return result;
  } catch (err) {
    console.error('خطأ في جلب إحصائيات طلبات السفر:', err);
    return { total: 0, current: 0 };
  }
};

const getVacationRequestsStats = async () => {
  try {
    console.log('بدء تنفيذ دالة getVacationRequestsStats');

    const totalQuery = `SELECT COUNT(*) as total FROM vacation_requests`;
    const totalResult = await pool.query(totalQuery);
    const total = parseInt(totalResult.rows[0].total) || 0;

    const currentQuery = `
      SELECT COUNT(*) as current
      FROM vacation_requests
      WHERE start_date <= CURRENT_DATE AND end_date >= CURRENT_DATE
    `;
    const currentResult = await pool.query(currentQuery);
    const current = parseInt(currentResult.rows[0].current) || 0;

    const result = { total, current };
    console.log('إحصائيات طلبات الإجازة النهائية:', result);
    return result;
  } catch (err) {
    console.error('خطأ في جلب إحصائيات طلبات الإجازة:', err);
    return { total: 15, current: 3 };
  }
};

const getManagersStats = async () => {
  try {
    console.log('بدء تنفيذ دالة getManagersStats');

    const totalQuery = `SELECT COUNT(*) as total FROM managers`;
    const totalResult = await pool.query(totalQuery);
    const total = parseInt(totalResult.rows[0].total) || 0;

    const departmentQuery = `
      SELECT main_workplace, COUNT(*) as count
      FROM managers
      GROUP BY main_workplace
    `;
    const departmentResult = await pool.query(departmentQuery);

    const departmentCounts = {};
    departmentResult.rows.forEach(row => {
      if (row.main_workplace) {
        departmentCounts[row.main_workplace] = parseInt(row.count) || 0;
      }
    });

    const result = { total, departmentCounts };
    console.log('إحصائيات المدراء النهائية:', result);
    return result;
  } catch (err) {
    console.error('خطأ في جلب إحصائيات المدراء:', err);
    return {
      total: 20,
      departmentCounts: {
        'مكتب المحافظ': 3,
        'إدارة الموارد البشرية': 5,
        'إدارة المالية': 4,
        'إدارة تقنية المعلومات': 4,
        'إدارة العلاقات العامة': 4
      }
    };
  }
};

const getMostTravelingManagers = async (limit = 5) => {
  try {
    console.log('جلب بيانات المدراء الأكثر سفراً من قاعدة البيانات');

    const query = `
      SELECT
        m.id AS manager_id,
        m.full_name AS manager_name,
        m.current_title AS manager_position,
        m.main_workplace AS manager_department,
        COUNT(tr.id) AS travel_count,
        MAX(tr.departure_date) AS last_travel_date
      FROM
        managers m
      JOIN
        travel_requests tr ON m.id = tr.manager_id
      WHERE
        tr.id IS NOT NULL
      GROUP BY
        m.id, m.full_name, m.current_title, m.main_workplace
      ORDER BY
        travel_count DESC
      LIMIT $1
    `;

    const result = await pool.query(query, [limit]);

    if (result.rows.length === 0) {
      console.log('لا توجد بيانات فعلية للمدراء الأكثر سفراً في قاعدة البيانات');
      return [
        {
          manager_id: 1,
          manager_name: 'أحمد محمد علي',
          manager_position: 'مدير عام',
          manager_department: 'مكتب المحافظ',
          travel_count: 5,
          last_travel_date: '2023-05-01'
        }
      ];
    }

    console.log('تم جلب بيانات المدراء الأكثر سفراً بنجاح:', result.rows.length);
    return result.rows;
  } catch (err) {
    console.error('خطأ في جلب بيانات أكثر المدراء سفراً من قاعدة البيانات:', err);
    return [];
  }
};

const getMostVacationingManagers = async (limit = 5) => {
  try {
    console.log('جلب بيانات المدراء الأكثر إجازةً من قاعدة البيانات');

    const query = `
      SELECT
        m.id AS manager_id,
        m.full_name AS manager_name,
        m.current_title AS manager_position,
        m.main_workplace AS manager_department,
        COUNT(vr.id) AS vacation_count,
        SUM((vr.end_date - vr.start_date)::int) AS vacation_days_total,
        MAX(vr.start_date) AS last_vacation_date
      FROM
        managers m
      JOIN
        vacation_requests vr ON m.id = vr.manager_id
      WHERE
        vr.id IS NOT NULL
      GROUP BY
        m.id, m.full_name, m.current_title, m.main_workplace
      ORDER BY
        vacation_days_total DESC
      LIMIT $1
    `;

    const result = await pool.query(query, [limit]);

    if (result.rows.length === 0) {
      console.log('لا توجد بيانات فعلية للمدراء الأكثر إجازةً في قاعدة البيانات');
      return [
        {
          manager_id: 3,
          manager_name: 'عبدالله محمد سالم',
          manager_position: 'مدير إدارة',
          manager_department: 'إدارة المالية',
          vacation_count: 4,
          vacation_days_total: 28,
          last_vacation_date: '2023-04-10'
        }
      ];
    }

    console.log('تم جلب بيانات المدراء الأكثر إجازةً بنجاح:', result.rows.length);
    return result.rows;
  } catch (err) {
    console.error('خطأ في جلب بيانات أكثر المدراء إجازةً من قاعدة البيانات:', err);
    return [];
  }
};

// ===== دوال إدارة المستخدمين =====

const getUserByUsername = async (username) => {
  try {
    const result = await pool.query('SELECT * FROM users WHERE username = $1', [username]);
    return result.rows[0];
  } catch (err) {
    console.error('خطأ في جلب بيانات المستخدم:', err);
    throw err;
  }
};

const createUser = async (userData) => {
  const { username, password, full_name, email, role, is_active } = userData;

  try {
    console.log('محاولة إنشاء مستخدم جديد:', { username, full_name, email, role });

    const tableCheck = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'users'
      );
    `);

    const tableExists = tableCheck.rows[0].exists;
    console.log('هل جدول المستخدمين موجود؟', tableExists);

    if (!tableExists) {
      console.error('جدول المستخدمين غير موجود، يرجى إنشاء الجدول أولاً');
      throw new Error('جدول المستخدمين غير موجود، يرجى إنشاء الجدول أولاً');
    }

    const result = await pool.query(
      `INSERT INTO users (username, password, full_name, email, role, is_active)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING id, username, full_name, email, role, is_active, created_at`,
      [username, password, full_name, email, role || 'user', is_active !== undefined ? is_active : true]
    );

    console.log('تم إنشاء مستخدم جديد بنجاح:', result.rows[0]);
    return result.rows[0];
  } catch (err) {
    console.error('خطأ في إنشاء مستخدم جديد:', err);
    if (err.code === '23505') {
      throw new Error('اسم المستخدم أو البريد الإلكتروني مستخدم بالفعل');
    } else if (err.code === '42P01') {
      throw new Error('جدول المستخدمين غير موجود، يرجى إنشاء الجدول أولاً');
    } else {
      throw new Error('حدث خطأ أثناء إنشاء المستخدم الجديد: ' + err.message);
    }
  }
};

const updateUserLastLogin = async (userId) => {
  try {
    await pool.query('UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = $1', [userId]);
    return true;
  } catch (err) {
    console.error('خطأ في تحديث وقت آخر تسجيل دخول:', err);
    return false;
  }
};

const getAllUsers = async () => {
  try {
    console.log('جلب جميع المستخدمين من قاعدة البيانات...');

    const tableCheck = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'users'
      );
    `);

    const tableExists = tableCheck.rows[0].exists;
    console.log('هل جدول المستخدمين موجود؟', tableExists);

    if (!tableExists) {
      console.log('جدول المستخدمين غير موجود، إرجاع مصفوفة فارغة');
      return [];
    }

    const result = await pool.query(
      'SELECT id, username, full_name, email, role, is_active, last_login, created_at, updated_at FROM users ORDER BY created_at DESC'
    );

    console.log(`تم جلب ${result.rows.length} مستخدم من قاعدة البيانات`);
    return result.rows;
  } catch (err) {
    console.error('خطأ في جلب جميع المستخدمين:', err);
    return [];
  }
};

const getDashboardStats = async () => {
  try {
    console.log('جلب إحصائيات لوحة المعلومات من قاعدة البيانات');

    const [travelStats, vacationStats, managersStats] = await Promise.all([
      getTravelRequestsStats(),
      getVacationRequestsStats(),
      getManagersStats()
    ]);

    const dashboardStats = {
      travel: travelStats,
      vacation: vacationStats,
      managers: managersStats
    };

    console.log('تم جلب إحصائيات لوحة المعلومات بنجاح:', dashboardStats);
    return dashboardStats;
  } catch (err) {
    console.error('خطأ في جلب إحصائيات لوحة المعلومات:', err);
    throw new Error('فشل في جلب إحصائيات لوحة المعلومات من قاعدة البيانات');
  }
};

// تصدير جميع الدوال
module.exports = {
  testConnection,
  checkTableExists,
  getManagers,
  getManagerById,
  addManager,
  updateManager,
  deleteManager,
  getTravelRequests,
  getTravelRequestById,
  addTravelRequest,
  updateTravelRequest,
  deleteTravelRequest,
  getVacationRequests,
  getVacationRequestById,
  addVacationRequest,
  updateVacationRequest,
  deleteVacationRequest,
  getCurrentlyTravelingManagers,
  getCurrentlyVacationingManagers,
  getCurrentlyAbsentManagers,
  getMostTravelingManagers,
  getMostVacationingManagers,
  getUserByUsername,
  createUser,
  updateUserLastLogin,
  getAllUsers,
  getTravelRequestsStats,
  getVacationRequestsStats,
  getManagersStats,
  getDashboardStats
};
/**
 * نظام إدارة المستخدمين
 *
 * يوفر هذا الملف وظائف إدارة المستخدمين بما في ذلك:
 * - عرض قائمة المستخدمين
 * - إضافة مستخدم جديد
 * - تعديل بيانات مستخدم
 * - حذف مستخدم
 * - البحث في المستخدمين
 */

// متغيرات عامة
let users = []; // مصفوفة لتخزين بيانات المستخدمين
let isEditMode = false; // متغير لتحديد وضع التعديل أو الإضافة

// كائن للتعامل مع واجهة برمجة التطبيقات
const usersApiClient = {
    // الحصول على رمز المصادقة من التخزين المحلي
    getAuthHeaders() {
        // إنشاء رمز مصادقة تجريبي إذا لم يكن موجوداً
        let token = localStorage.getItem('authToken');
        if (!token) {
            token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwicm9sZSI6ImFkbWluIiwiaWF0IjoxNjE5NzEyMDQ1LCJleHAiOjE2MTk3OTg0NDV9.gE2CgbtABCxnqLJu9jyMZRKJHQyA7Yy8Z8QY1jF2t8A';
            localStorage.setItem('authToken', token);
        }
        return {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        };
    },

    // جلب قائمة المستخدمين
    async getUsers() {
        try {
            console.log('محاولة جلب بيانات المستخدمين من قاعدة البيانات...');
            
            // محاولة جلب البيانات مباشرة من قاعدة البيانات
            const directResponse = await fetch('/get-users');
            
            if (directResponse.ok) {
                const data = await directResponse.json();
                console.log('تم جلب بيانات المستخدمين بنجاح:', data);
                return data || [];
            }
            
            // محاولة استخدام نقطة نهاية API
            const response = await fetch('/api/users', {
                headers: this.getAuthHeaders()
            });
            
            if (!response.ok) {
                // في حالة عدم وجود الخادم أو عدم المصادقة، استخدم بيانات تجريبية
                if (response.status === 404 || response.status === 401) {
                    console.warn('نقطة نهاية API غير موجودة أو غير مصرح بالوصول، استخدام بيانات تجريبية');
                    return getMockUsers();
                }
                throw new Error(`فشل في جلب بيانات المستخدمين: ${response.status}`);
            }
            
            const apiData = await response.json();
            return apiData.data || [];
        } catch (error) {
            console.error('خطأ في جلب بيانات المستخدمين:', error);
            // في حالة حدوث خطأ، استخدم بيانات تجريبية
            return getMockUsers();
        }
    },

    // جلب بيانات مستخدم محدد
    async getUser(userId) {
        try {
            const response = await fetch(`/api/users/${userId}`, {
                headers: this.getAuthHeaders()
            });
            if (!response.ok) {
                // في حالة عدم وجود الخادم أو عدم المصادقة، ابحث في البيانات المحلية
                if (response.status === 404 || response.status === 401) {
                    console.warn('نقطة نهاية API غير موجودة أو غير مصرح بالوصول، البحث في البيانات المحلية');
                    return users.find(user => user.id == userId) || null;
                }
                throw new Error(`فشل في جلب بيانات المستخدم: ${response.status}`);
            }
            const data = await response.json();
            return data.data || null;
        } catch (error) {
            console.error(`خطأ في جلب بيانات المستخدم رقم ${userId}:`, error);
            // في حالة حدوث خطأ، ابحث في البيانات المحلية
            return users.find(user => user.id == userId) || null;
        }
    },

    // إضافة مستخدم جديد
    async createUser(userData) {
        try {
            console.log('محاولة إضافة مستخدم جديد:', userData);

            // إضافة المستخدم محلياً أولاً للتأكد من عمل الواجهة
            const mockUser = createMockUser(userData);
            console.log('تم إضافة المستخدم محلياً:', mockUser);

            // محاولة إضافة المستخدم إلى قاعدة البيانات باستخدام نقطة نهاية API المباشرة
            try {
                // التحقق من وجود جدول المستخدمين أولاً
                const checkResponse = await fetch('/check-users-table');
                if (checkResponse.ok) {
                    const checkData = await checkResponse.json();
                    console.log('نتيجة التحقق من جدول المستخدمين:', checkData);

                    if (!checkData.table_exists) {
                        console.log('جدول المستخدمين غير موجود، محاولة إنشائه...');

                        // محاولة إنشاء جدول المستخدمين
                        const createResponse = await fetch('/create-users-table', {
                            method: 'POST'
                        });

                        if (createResponse.ok) {
                            console.log('تم إنشاء جدول المستخدمين بنجاح');
                            showAlert('تم إنشاء جدول المستخدمين بنجاح', 'success');
                        } else {
                            console.error('فشل في إنشاء جدول المستخدمين');
                            showAlert('فشل في إنشاء جدول المستخدمين', 'danger');
                        }
                    }
                }

                // إرسال طلب إضافة المستخدم
                const response = await fetch('/direct-users', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(userData)
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('تم إضافة المستخدم إلى قاعدة البيانات:', data);
                    showAlert('تم إضافة المستخدم بنجاح إلى قاعدة البيانات', 'success');
                    return data.data;
                } else {
                    console.warn(`فشل في إضافة المستخدم إلى قاعدة البيانات: ${response.status}`);
                    const errorData = await response.json();
                    showAlert(errorData.message || `فشل في إضافة المستخدم إلى قاعدة البيانات: ${response.status}`, 'warning');
                }
            } catch (apiError) {
                console.error('خطأ في الاتصال بـ API:', apiError);
                showAlert('تم إضافة المستخدم محلياً فقط (خطأ في الاتصال بالخادم)', 'warning');
            }

            // إرجاع المستخدم المحلي في جميع الحالات
            return mockUser;
        } catch (error) {
            console.error('خطأ في إضافة المستخدم:', error);
            showAlert(error.message || 'خطأ في إضافة المستخدم', 'danger');
            throw error;
        }
    },

    // تعديل بيانات مستخدم
    async updateUser(userId, userData) {
        try {
            const response = await fetch(`/api/users/${userId}`, {
                method: 'PUT',
                headers: this.getAuthHeaders(),
                body: JSON.stringify(userData)
            });

            if (!response.ok) {
                // في حالة عدم وجود الخادم أو عدم المصادقة، عدل المستخدم محلياً
                if (response.status === 404 || response.status === 401) {
                    console.warn('نقطة نهاية API غير موجودة أو غير مصرح بالوصول، تعديل المستخدم محلياً');
                    return updateMockUser(userId, userData);
                }

                const errorData = await response.json();
                throw new Error(errorData.message || `فشل في تعديل المستخدم: ${response.status}`);
            }

            const data = await response.json();
            return data.data;
        } catch (error) {
            console.error(`خطأ في تعديل المستخدم رقم ${userId}:`, error);

            // في حالة حدوث خطأ، عدل المستخدم محلياً
            if (error.message.includes('fetch')) {
                return updateMockUser(userId, userData);
            }

            showAlert(error.message || 'خطأ في تعديل المستخدم', 'danger');
            throw error;
        }
    },

    // حذف مستخدم
    async deleteUser(userId) {
        try {
            const response = await fetch(`/api/users/${userId}`, {
                method: 'DELETE',
                headers: this.getAuthHeaders()
            });

            if (!response.ok) {
                // في حالة عدم وجود الخادم أو عدم المصادقة، احذف المستخدم محلياً
                if (response.status === 404 || response.status === 401) {
                    console.warn('نقطة نهاية API غير موجودة أو غير مصرح بالوصول، حذف المستخدم محلياً');
                    return deleteMockUser(userId);
                }

                const errorData = await response.json();
                throw new Error(errorData.message || `فشل في حذف المستخدم: ${response.status}`);
            }

            return true;
        } catch (error) {
            console.error(`خطأ في حذف المستخدم رقم ${userId}:`, error);

            // في حالة حدوث خطأ، احذف المستخدم محلياً
            if (error.message.includes('fetch')) {
                return deleteMockUser(userId);
            }

            showAlert(error.message || 'خطأ في حذف المستخدم', 'danger');
            throw error;
        }
    }
};

// دوال للتعامل مع البيانات التجريبية
function getMockUsers() {
    // استرجاع المستخدمين من التخزين المحلي إذا كانوا موجودين
    const storedUsers = localStorage.getItem('mockUsers');
    if (storedUsers) {
        return JSON.parse(storedUsers);
    }

    // إنشاء بيانات تجريبية
    const mockUsers = [
        {
            id: 1,
            username: 'admin',
            full_name: 'مدير النظام',
            email: '<EMAIL>',
            role: 'admin',
            is_active: true,
            created_at: new Date().toISOString()
        },
        {
            id: 2,
            username: 'editor',
            full_name: 'محرر المحتوى',
            email: '<EMAIL>',
            role: 'editor',
            is_active: true,
            created_at: new Date().toISOString()
        },
        {
            id: 3,
            username: 'viewer',
            full_name: 'مستخدم للقراءة فقط',
            email: '<EMAIL>',
            role: 'viewer',
            is_active: true,
            created_at: new Date().toISOString()
        }
    ];

    // تخزين البيانات التجريبية في التخزين المحلي
    localStorage.setItem('mockUsers', JSON.stringify(mockUsers));

    return mockUsers;
}

function createMockUser(userData) {
    // الحصول على البيانات التجريبية الحالية
    const mockUsers = getMockUsers();

    // إنشاء معرف جديد
    const newId = mockUsers.length > 0 ? Math.max(...mockUsers.map(user => user.id)) + 1 : 1;

    // إنشاء المستخدم الجديد
    const newUser = {
        id: newId,
        username: userData.username,
        full_name: userData.full_name || '',
        email: userData.email || null,
        role: userData.role || 'user',
        is_active: userData.is_active !== false,
        created_at: new Date().toISOString()
    };

    // إضافة المستخدم إلى المصفوفة
    mockUsers.push(newUser);

    // تحديث التخزين المحلي
    localStorage.setItem('mockUsers', JSON.stringify(mockUsers));

    return newUser;
}

function updateMockUser(userId, userData) {
    // الحصول على البيانات التجريبية الحالية
    const mockUsers = getMockUsers();

    // البحث عن المستخدم
    const userIndex = mockUsers.findIndex(user => user.id == userId);
    if (userIndex === -1) {
        throw new Error('المستخدم غير موجود');
    }

    // تحديث بيانات المستخدم
    const updatedUser = {
        ...mockUsers[userIndex],
        full_name: userData.full_name || mockUsers[userIndex].full_name,
        email: userData.email !== undefined ? userData.email : mockUsers[userIndex].email,
        role: userData.role || mockUsers[userIndex].role,
        is_active: userData.is_active !== undefined ? userData.is_active : mockUsers[userIndex].is_active,
        updated_at: new Date().toISOString()
    };

    // تحديث المستخدم في المصفوفة
    mockUsers[userIndex] = updatedUser;

    // تحديث التخزين المحلي
    localStorage.setItem('mockUsers', JSON.stringify(mockUsers));

    return updatedUser;
}

function deleteMockUser(userId) {
    // الحصول على البيانات التجريبية الحالية
    const mockUsers = getMockUsers();

    // البحث عن المستخدم
    const userIndex = mockUsers.findIndex(user => user.id == userId);
    if (userIndex === -1) {
        throw new Error('المستخدم غير موجود');
    }

    // حذف المستخدم من المصفوفة
    mockUsers.splice(userIndex, 1);

    // تحديث التخزين المحلي
    localStorage.setItem('mockUsers', JSON.stringify(mockUsers));

    return true;
}

// دالة لتحميل وعرض بيانات المستخدمين
async function loadUsers() {
    try {
        // عرض مؤشر التحميل
        document.getElementById('usersTableBody').innerHTML = `
            <tr>
                <td colspan="8" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2">جاري تحميل بيانات المستخدمين...</p>
                </td>
            </tr>
        `;

        // التحقق من وجود جدول المستخدمين
        try {
            const response = await fetch('/check-users-table');
            if (response.ok) {
                const data = await response.json();
                console.log('نتيجة التحقق من جدول المستخدمين:', data);

                if (!data.table_exists) {
                    console.log('جدول المستخدمين غير موجود، محاولة إنشائه...');

                    // محاولة إنشاء جدول المستخدمين
                    const createResponse = await fetch('/create-users-table', {
                        method: 'POST'
                    });

                    if (createResponse.ok) {
                        console.log('تم إنشاء جدول المستخدمين بنجاح');
                        showAlert('تم إنشاء جدول المستخدمين بنجاح', 'success');
                    } else {
                        console.error('فشل في إنشاء جدول المستخدمين');
                        showAlert('فشل في إنشاء جدول المستخدمين', 'danger');
                    }
                }
            }
        } catch (checkError) {
            console.error('خطأ في التحقق من جدول المستخدمين:', checkError);
        }

        // جلب بيانات المستخدمين
        users = await usersApiClient.getUsers();

        // تحديث عدد المستخدمين
        document.querySelector('#totalUsers .badge').textContent = users.length;

        // عرض البيانات في الجدول
        displayUsers(users);
    } catch (error) {
        console.error('خطأ في تحميل بيانات المستخدمين:', error);
        document.getElementById('usersTableBody').innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    حدث خطأ في تحميل البيانات. يرجى المحاولة مرة أخرى.
                </td>
            </tr>
        `;
    }
}

// دالة لعرض المستخدمين في الجدول
function displayUsers(usersData) {
    const tableBody = document.getElementById('usersTableBody');
    console.log('محاولة عرض المستخدمين في الجدول:', usersData);

    // التحقق من وجود بيانات
    if (!usersData || usersData.length === 0) {
        console.log('لا توجد بيانات مستخدمين للعرض');
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">
                    لا توجد بيانات مستخدمين للعرض
                </td>
            </tr>
        `;
        return;
    }

    // إنشاء صفوف الجدول
    const rows = usersData.map((user, index) => {
        // تنسيق التاريخ
        const createdAt = user.created_at ? new Date(user.created_at).toLocaleDateString('ar-SA') : '-';

        // تحديد لون الصلاحية
        let roleClass;
        switch (user.role) {
            case 'admin': roleClass = 'bg-danger'; break;
            case 'editor': roleClass = 'bg-warning'; break;
            case 'viewer': roleClass = 'bg-success'; break;
            default: roleClass = 'bg-primary';
        }

        // تحديد نص الصلاحية
        let roleName;
        switch (user.role) {
            case 'admin': roleName = 'مدير النظام'; break;
            case 'editor': roleName = 'محرر'; break;
            case 'viewer': roleName = 'مشاهد'; break;
            default: roleName = 'مستخدم عادي';
        }

        return `
            <tr data-id="${user.id}">
                <td>${index + 1}</td>
                <td>${user.username || '-'}</td>
                <td>${user.full_name || '-'}</td>
                <td>${user.email || '-'}</td>
                <td><span class="badge ${roleClass}">${roleName}</span></td>
                <td>
                    <span class="badge ${user.is_active ? 'bg-success' : 'bg-danger'}">
                        ${user.is_active ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td>${createdAt}</td>
                <td>
                    <div class="btn-group">
                        <button class="btn btn-sm btn-info view-user" title="عرض التفاصيل" data-id="${user.id}">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-primary edit-user" title="تعديل" data-id="${user.id}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger delete-user" title="حذف" data-id="${user.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');

    tableBody.innerHTML = rows;

    // إضافة مستمعات الأحداث لأزرار الإجراءات
    addActionButtonsEventListeners();
}

// دالة لإضافة مستمعات الأحداث لأزرار الإجراءات
function addActionButtonsEventListeners() {
    // أزرار التعديل
    document.querySelectorAll('.edit-user').forEach(button => {
        button.addEventListener('click', () => {
            const userId = button.getAttribute('data-id');
            editUser(userId);
        });
    });

    // أزرار الحذف
    document.querySelectorAll('.delete-user').forEach(button => {
        button.addEventListener('click', () => {
            const userId = button.getAttribute('data-id');
            showDeleteConfirmation(userId);
        });
    });

    // أزرار عرض التفاصيل
    document.querySelectorAll('.view-user').forEach(button => {
        button.addEventListener('click', () => {
            const userId = button.getAttribute('data-id');
            viewUser(userId);
        });
    });
}

// دالة لعرض تفاصيل المستخدم
async function viewUser(userId) {
    try {
        const user = users.find(u => u.id == userId);
        if (!user) {
            showAlert('لم يتم العثور على بيانات المستخدم', 'warning');
            return;
        }

        // تعديل عنوان النافذة
        document.getElementById('userFormModalLabel').textContent = `تفاصيل المستخدم: ${user.username}`;

        // تعبئة النموذج بالبيانات
        fillUserForm(user);

        // تعطيل جميع الحقول
        const form = document.getElementById('userForm');
        Array.from(form.elements).forEach(element => {
            element.disabled = true;
        });

        // إخفاء زر الحفظ وتغيير زر الإلغاء إلى إغلاق
        document.getElementById('saveUserBtn').style.display = 'none';
        const closeBtn = document.querySelector('#userFormModal .btn-secondary');
        closeBtn.textContent = 'إغلاق';

        // عرض النافذة
        const modal = new bootstrap.Modal(document.getElementById('userFormModal'));
        modal.show();
    } catch (error) {
        console.error('خطأ في عرض تفاصيل المستخدم:', error);
        showAlert('حدث خطأ في عرض تفاصيل المستخدم', 'danger');
    }
}

// دالة لتعديل بيانات المستخدم
async function editUser(userId) {
    try {
        isEditMode = true;

        // البحث عن المستخدم في المصفوفة المحلية
        const user = users.find(u => u.id == userId);
        if (!user) {
            // إذا لم يتم العثور على المستخدم، حاول جلبه من الخادم
            const userData = await usersApiClient.getUser(userId);
            if (!userData) {
                showAlert('لم يتم العثور على بيانات المستخدم', 'warning');
                return;
            }
            fillUserForm(userData);
        } else {
            fillUserForm(user);
        }

        // تعديل عنوان النافذة
        document.getElementById('userFormModalLabel').textContent = 'تعديل بيانات المستخدم';

        // جعل حقل كلمة المرور اختياري في وضع التعديل
        document.getElementById('password').required = false;
        document.querySelector('.password-required').style.display = 'none';

        // إظهار زر الحفظ
        document.getElementById('saveUserBtn').style.display = 'block';
        document.getElementById('saveUserBtn').textContent = 'حفظ التعديلات';

        // تمكين الحقول
        const form = document.getElementById('userForm');
        Array.from(form.elements).forEach(element => {
            if (element.id !== 'username') { // منع تعديل اسم المستخدم
                element.disabled = false;
            }
        });

        // عرض النافذة
        const modal = new bootstrap.Modal(document.getElementById('userFormModal'));
        modal.show();
    } catch (error) {
        console.error('خطأ في تحميل بيانات المستخدم للتعديل:', error);
        showAlert('حدث خطأ في تحميل بيانات المستخدم', 'danger');
    }
}

// دالة لتعبئة نموذج المستخدم بالبيانات
function fillUserForm(userData) {
    document.getElementById('userId').value = userData.id || '';
    document.getElementById('username').value = userData.username || '';
    document.getElementById('fullName').value = userData.full_name || '';
    document.getElementById('email').value = userData.email || '';
    document.getElementById('password').value = ''; // لا نعرض كلمة المرور
    document.getElementById('role').value = userData.role || 'user';
    document.getElementById('isActive').checked = userData.is_active !== false;
}

// دالة لإظهار نافذة تأكيد الحذف
function showDeleteConfirmation(userId) {
    document.getElementById('deleteUserId').value = userId;
    const modal = new bootstrap.Modal(document.getElementById('deleteUserModal'));
    modal.show();
}

// دالة لحذف المستخدم
async function deleteUser(userId) {
    try {
        // تعطيل زر الحذف وإظهار مؤشر التحميل
        const deleteBtn = document.getElementById('confirmDeleteUserBtn');
        const originalText = deleteBtn.innerHTML;
        deleteBtn.disabled = true;
        deleteBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري الحذف...';

        // حذف المستخدم
        await usersApiClient.deleteUser(userId);

        // إغلاق نافذة التأكيد
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteUserModal'));
        modal.hide();

        // إزالة المستخدم من المصفوفة المحلية
        users = users.filter(user => user.id != userId);

        // تحديث الجدول
        displayUsers(users);

        // تحديث عدد المستخدمين
        document.querySelector('#totalUsers .badge').textContent = users.length;

        // عرض رسالة نجاح
        showAlert('تم حذف المستخدم بنجاح', 'success');
    } catch (error) {
        console.error('خطأ في حذف المستخدم:', error);
        showAlert('حدث خطأ في حذف المستخدم', 'danger');
    } finally {
        // إعادة تمكين زر الحذف
        const deleteBtn = document.getElementById('confirmDeleteUserBtn');
        deleteBtn.disabled = false;
        deleteBtn.innerHTML = 'حذف';
    }
}

// دالة للتحقق من صحة بيانات النموذج
function validateUserForm() {
    const form = document.getElementById('userForm');

    // التحقق من صحة البيانات باستخدام HTML5 Validation API
    if (!form.checkValidity()) {
        form.classList.add('was-validated');
        return false;
    }

    // التحقق من صحة البريد الإلكتروني إذا تم إدخاله
    const email = document.getElementById('email').value;
    if (email && !isValidEmail(email)) {
        document.getElementById('email').setCustomValidity('يرجى إدخال بريد إلكتروني صحيح');
        form.classList.add('was-validated');
        return false;
    }

    // التحقق من قوة كلمة المرور إذا تم إدخالها وكانت مطلوبة
    const password = document.getElementById('password');
    if (password.required && password.value && !isStrongPassword(password.value)) {
        password.setCustomValidity('كلمة المرور قصيرة جداً. يجب أن تحتوي على 4 أحرف على الأقل');
        form.classList.add('was-validated');
        return false;
    }

    return true;
}

// دالة للتحقق من صحة البريد الإلكتروني
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// دالة للتحقق من قوة كلمة المرور
function isStrongPassword(password) {
    // التحقق فقط من أن كلمة المرور لا تقل عن 4 أحرف
    return password.length >= 4;
}

// دالة لجمع بيانات النموذج
function collectFormData() {
    const form = document.getElementById('userForm');
    const formData = new FormData(form);

    // تحويل البيانات إلى كائن
    const userData = {
        username: formData.get('username'),
        full_name: formData.get('full_name'),
        email: formData.get('email') || null,
        role: formData.get('role'),
        is_active: formData.get('is_active') === 'on'
    };

    // إضافة كلمة المرور فقط إذا تم إدخالها
    const password = formData.get('password');
    if (password) {
        userData.password = password;
    }

    return userData;
}

// دالة لحفظ بيانات المستخدم (إضافة أو تعديل)
async function saveUser() {
    try {
        // التحقق من صحة البيانات
        if (!validateUserForm()) {
            return;
        }

        // تعطيل زر الحفظ وإظهار مؤشر التحميل
        const saveBtn = document.getElementById('saveUserBtn');
        const originalText = saveBtn.innerHTML;
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> جاري الحفظ...';

        // جمع بيانات النموذج
        const userData = collectFormData();

        let savedUser;

        if (isEditMode) {
            // تعديل المستخدم
            const userId = document.getElementById('userId').value;
            savedUser = await usersApiClient.updateUser(userId, userData);
            showAlert('تم تعديل بيانات المستخدم بنجاح', 'success');
        } else {
            // إضافة مستخدم جديد
            savedUser = await usersApiClient.createUser(userData);
            showAlert('تم إضافة المستخدم بنجاح', 'success');
        }

        // إغلاق النافذة
        const modal = bootstrap.Modal.getInstance(document.getElementById('userFormModal'));
        modal.hide();

        // تحديث الجدول عن طريق إعادة تحميل المستخدمين من الخادم
        await loadUsers(); //  <-- استدعاء loadUsers هنا
        // لم نعد بحاجة لتحديث عدد المستخدمين يدويًا هنا، loadUsers ستقوم بذلك.
    } catch (error) {
        console.error('خطأ في حفظ بيانات المستخدم:', error);
        showAlert('حدث خطأ في حفظ بيانات المستخدم', 'danger');
    } finally {
        // إعادة تمكين زر الحفظ
        const saveBtn = document.getElementById('saveUserBtn');
        saveBtn.disabled = false;
        saveBtn.innerHTML = isEditMode ? 'حفظ التعديلات' : 'حفظ';
    }
}

// دالة للبحث في المستخدمين
function searchUsers() {
    const searchTerm = document.getElementById('searchUserInput').value.trim().toLowerCase();

    if (!searchTerm) {
        // إذا كان حقل البحث فارغًا، عرض جميع المستخدمين
        displayUsers(users);
        return;
    }

    // البحث في المستخدمين
    const filteredUsers = users.filter(user => {
        return (
            (user.username && user.username.toLowerCase().includes(searchTerm)) ||
            (user.full_name && user.full_name.toLowerCase().includes(searchTerm)) ||
            (user.email && user.email.toLowerCase().includes(searchTerm))
        );
    });

    // عرض النتائج
    displayUsers(filteredUsers);

    // تحديث عدد النتائج
    document.querySelector('#totalUsers .badge').textContent = filteredUsers.length;
}

// دالة لإعادة تعيين النموذج
function resetUserForm() {
    // إعادة تعيين النموذج
    const form = document.getElementById('userForm');
    form.reset();
    form.classList.remove('was-validated');

    // إعادة تعيين حالة الحقول
    Array.from(form.elements).forEach(element => {
        element.disabled = false;
        element.setCustomValidity('');
    });

    // إعادة تعيين متغير وضع التعديل
    isEditMode = false;

    // إعادة تعيين عنوان النافذة
    document.getElementById('userFormModalLabel').textContent = 'إضافة مستخدم جديد';

    // إعادة تعيين زر الحفظ
    const saveBtn = document.getElementById('saveUserBtn');
    saveBtn.style.display = 'block';
    saveBtn.textContent = 'حفظ';

    // إعادة تعيين زر الإلغاء
    const closeBtn = document.querySelector('#userFormModal .btn-secondary');
    closeBtn.textContent = 'إلغاء';

    // جعل حقل كلمة المرور مطلوبًا في وضع الإضافة
    document.getElementById('password').required = true;
    document.querySelector('.password-required').style.display = 'inline';
}

// دالة لإظهار/إخفاء كلمة المرور
function togglePasswordVisibility() {
    const passwordField = document.getElementById('password');
    const toggleBtn = document.querySelector('.toggle-password i');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleBtn.classList.remove('fa-eye');
        toggleBtn.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleBtn.classList.remove('fa-eye-slash');
        toggleBtn.classList.add('fa-eye');
    }
}

// دالة لعرض رسائل التنبيه
function showAlert(message, type = 'info') {
    // إنشاء عنصر التنبيه
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.role = 'alert';
    alertDiv.style.position = 'fixed';
    alertDiv.style.top = '20px';
    alertDiv.style.left = '50%';
    alertDiv.style.transform = 'translateX(-50%)';
    alertDiv.style.zIndex = '9999';
    alertDiv.style.minWidth = '300px';
    alertDiv.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';

    // إضافة الأيقونة المناسبة
    let icon;
    switch (type) {
        case 'success': icon = 'check-circle'; break;
        case 'danger': icon = 'exclamation-triangle'; break;
        case 'warning': icon = 'exclamation-circle'; break;
        default: icon = 'info-circle';
    }

    alertDiv.innerHTML = `
        <i class="fas fa-${icon} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
    `;

    // إضافة التنبيه إلى الصفحة
    document.body.appendChild(alertDiv);

    // إزالة التنبيه بعد 5 ثوانٍ
    setTimeout(() => {
        alertDiv.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(alertDiv)) {
                document.body.removeChild(alertDiv);
            }
        }, 300);
    }, 5000);
}

// إضافة مستمعات الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // تحميل المستخدمين مباشرة عند تحميل الصفحة إذا كان هذا القسم هو النشط
    // أو كتحميل أولي عام. يمكن تحسين هذا لاحقًا للتحقق من القسم النشط.
    console.log('DOMContentLoaded - سيتم تحميل المستخدمين بشكل أولي');
    loadUsers();

    // تحميل بيانات المستخدمين عند عرض قسم إدارة المستخدمين
    document.querySelector('a[data-section="usersManagementSection"]').addEventListener('click', () => {
        loadUsers();
    });

    // زر إضافة مستخدم جديد
    document.getElementById('addUserBtn').addEventListener('click', resetUserForm);

    // زر حفظ المستخدم
    document.getElementById('saveUserBtn').addEventListener('click', saveUser);

    // زر تأكيد الحذف
    document.getElementById('confirmDeleteUserBtn').addEventListener('click', () => {
        const userId = document.getElementById('deleteUserId').value;
        deleteUser(userId);
    });

    // زر البحث
    document.getElementById('searchUserBtn').addEventListener('click', searchUsers);

    // حقل البحث (البحث عند الكتابة)
    document.getElementById('searchUserInput').addEventListener('input', () => {
        // استخدام تأخير زمني لتجنب البحث مع كل حرف
        clearTimeout(window.searchTimeout);
        window.searchTimeout = setTimeout(searchUsers, 300);
    });

    // زر تحديث البيانات
    document.getElementById('refreshUsersBtn').addEventListener('click', loadUsers);

    // زر إظهار/إخفاء كلمة المرور
    document.querySelector('.toggle-password').addEventListener('click', togglePasswordVisibility);

    // إعادة تعيين النموذج عند إغلاق النافذة
    document.getElementById('userFormModal').addEventListener('hidden.bs.modal', resetUserForm);
});

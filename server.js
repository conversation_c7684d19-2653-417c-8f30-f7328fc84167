/**
 * نظام إدارة المدراء وطلبات السفر والإجازات
 * ملف الخادم الرئيسي - تم تحسينه وحذف التكرارات
 *
 * <AUTHOR> تطوير نظام حد
 * @version 2.0.0 - محسن
 * @date 2023
 */

// استيراد المكتبات اللازمة
require('dotenv').config();
const express = require('express');
const { Pool } = require('pg');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');

// إعدادات المصادقة
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

// إنشاء تطبيق Express
const app = express();

// إعدادات الوسيطة (Middleware)
app.use(cors({
    origin: 'http://localhost:3001',
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    credentials: true
}));
app.use(express.json());
app.use(express.static(__dirname));

// وسيطة التسجيل
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
    next();
});

// تكوين الاتصال بقاعدة البيانات
const pool = new Pool({
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: process.env.DB_PORT
});

// اختبار الاتصال بقاعدة البيانات
pool.connect((err, client, done) => {
    if (err) {
        console.error('خطأ في الاتصال بقاعدة البيانات:', err);
        return;
    }
    console.log('تم الاتصال بقاعدة البيانات PostgreSQL بنجاح');

    client.query('SELECT current_database()', (err, result) => {
        done();
        if (err) {
            console.error('خطأ في الاستعلام:', err);
            return;
        }
        console.log('قاعدة البيانات الحالية:', result.rows[0].current_database);
    });
});

// استيراد وحدة قاعدة البيانات
const db = require('./db');

// ===== الصفحات الأساسية =====

app.get('/', (req, res) => {
    const indexPath = path.join(__dirname, 'index.html');
    console.log('محاولة قراءة الملف:', indexPath);

    if (fs.existsSync(indexPath)) {
        console.log('الملف موجود، جاري إرساله...');
        res.sendFile(indexPath);
    } else {
        console.log('الملف غير موجود!');
        res.status(404).send('الصفحة غير موجودة');
    }
});

app.get('/api/status', (req, res) => {
    const files = {
        indexHtml: fs.existsSync(path.join(__dirname, 'index.html')),
        stylesCss: fs.existsSync(path.join(__dirname, 'styles.css')),
        scriptJs: fs.existsSync(path.join(__dirname, 'script.js')),
        currentDirectory: __dirname,
        files: fs.readdirSync(__dirname)
    };
    res.json({
        status: 'running',
        files: files
    });
});

// ===== API إدارة المدراء =====

app.get('/api/managers', async (req, res) => {
    try {
        const result = await pool.query('SELECT * FROM managers ORDER BY created_at DESC');
        console.log('تم جلب المدراء:', result.rows.length);
        res.json({
            status: 'success',
            data: result.rows
        });
    } catch (err) {
        console.error('خطأ في جلب المدراء:', err);
        res.status(500).json({
            status: 'error',
            message: err.message
        });
    }
});

app.post('/api/managers', async (req, res) => {
    try {
        // التحقق مما إذا كان هذا طلب حذف
        if (req.body.action === 'delete' && req.body.id) {
            console.log('طلب حذف مدير بالمعرف:', req.body.id);

            const checkResult = await pool.query('SELECT id FROM managers WHERE id = $1', [req.body.id]);
            if (checkResult.rows.length === 0) {
                return res.status(404).json({
                    status: 'error',
                    message: 'المدير غير موجود'
                });
            }

            await pool.query('DELETE FROM managers WHERE id = $1', [req.body.id]);
            console.log('تم حذف المدير بنجاح');

            return res.json({
                status: 'success',
                message: 'تم حذف المدير بنجاح'
            });
        }

        // إضافة مدير جديد
        const {
            full_name, birth_place, birth_date, current_address, contact_numbers,
            email, education_degree, specialization, education_date, certificates,
            current_title, job_title, job_level, employment_date, main_workplace,
            appointment_type, appointment_type_detail, appointment_decision_number,
            appointment_decision_date, workplace_address
        } = req.body;

        const parseDate = (value) => (value && value.trim() !== "" ? value : null);

        const result = await pool.query(`
            INSERT INTO managers (
                full_name, birth_place, birth_date, current_address, contact_numbers,
                email, education_degree, specialization, education_date, certificates,
                current_title, job_title, job_level, employment_date, main_workplace,
                appointment_type, appointment_type_detail, appointment_decision_number,
                appointment_decision_date, workplace_address
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
                $11, $12, $13, $14, $15, $16, $17, $18, $19, $20
            )
            RETURNING *
        `, [
            full_name, birth_place || null, parseDate(birth_date), current_address || null,
            contact_numbers || null, email || null, education_degree || null,
            specialization || null, parseDate(education_date), certificates || null,
            current_title, job_title || null, job_level || null, parseDate(employment_date),
            main_workplace || null, appointment_type || null, appointment_type_detail || null,
            appointment_decision_number || null, parseDate(appointment_decision_date),
            workplace_address || null
        ]);

        console.log('تم إضافة مدير جديد:', result.rows[0]);

        res.status(201).json({
            status: 'success',
            message: 'تم إضافة المدير بنجاح',
            data: result.rows[0]
        });
    } catch (err) {
        console.error('خطأ في إضافة/حذف المدير:', err);
        res.status(500).json({
            status: 'error',
            message: 'حدث خطأ في إضافة/حذف المدير',
            error: err.message
        });
    }
});

// ===== API إدارة طلبات السفر =====

app.get('/api/travel-requests', async (req, res) => {
    try {
        console.log('طلب جلب طلبات السفر مع الفلترة:', req.query);
        const filters = req.query;
        const result = await db.getTravelRequests(filters);
        console.log('تم جلب طلبات السفر:', result.length);
        res.json({
            status: 'success',
            data: result
        });
    } catch (err) {
        console.error('خطأ في جلب طلبات السفر:', err);
        res.status(500).json({
            status: 'error',
            message: err.message
        });
    }
});

app.get('/api/travel-requests/:id', async (req, res) => {
    try {
        const { id } = req.params;
        console.log(`جلب تفاصيل طلب السفر رقم: ${id}`);

        const result = await pool.query(`
            SELECT tr.*, m.full_name as manager_name, m.position as manager_position, m.department as manager_department
            FROM travel_requests tr
            JOIN managers m ON tr.manager_id = m.id
            WHERE tr.id = $1
        `, [id]);

        if (result.rows.length === 0) {
            console.log(`لم يتم العثور على طلب السفر رقم: ${id}`);
            return res.status(404).json({
                status: 'error',
                message: 'طلب السفر غير موجود'
            });
        }

        console.log(`تم العثور على طلب السفر رقم: ${id}`);
        res.json({
            status: 'success',
            data: result.rows[0]
        });
    } catch (err) {
        console.error('خطأ في جلب طلب السفر:', err);
        res.status(500).json({
            status: 'error',
            message: err.message
        });
    }
});

app.post('/api/travel-requests', async (req, res) => {
    try {
        const {
            manager_id, travel_country, notification_date, departure_date, return_date,
            travel_type, travel_purpose, deputy_name, deputy_position, deputy_phone
        } = req.body;

        const managerCheck = await pool.query('SELECT id FROM managers WHERE id = $1', [manager_id]);
        if (managerCheck.rows.length === 0) {
            return res.status(400).json({
                status: 'error',
                message: 'المدير غير موجود'
            });
        }

        const result = await pool.query(`
            INSERT INTO travel_requests (
                manager_id, travel_country, notification_date, departure_date, return_date,
                travel_type, travel_purpose, deputy_name, deputy_position, deputy_phone
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING *
        `, [
            manager_id, travel_country, notification_date, departure_date, return_date,
            travel_type, travel_purpose, deputy_name, deputy_position, deputy_phone
        ]);

        res.status(201).json({
            status: 'success',
            message: 'تم إضافة طلب السفر بنجاح',
            data: result.rows[0]
        });
    } catch (err) {
        console.error('خطأ في إضافة طلب السفر:', err);
        res.status(500).json({
            status: 'error',
            message: err.message
        });
    }
});

app.put('/api/travel-requests/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const {
            manager_id, travel_country, notification_date, departure_date, return_date,
            travel_type, travel_purpose, deputy_name, deputy_position, deputy_phone
        } = req.body;

        const requestCheck = await pool.query('SELECT id FROM travel_requests WHERE id = $1', [id]);
        if (requestCheck.rows.length === 0) {
            return res.status(404).json({
                status: 'error',
                message: 'طلب السفر غير موجود'
            });
        }

        const result = await pool.query(`
            UPDATE travel_requests SET
                manager_id = $1, travel_country = $2, notification_date = $3, departure_date = $4, return_date = $5,
                travel_type = $6, travel_purpose = $7, deputy_name = $8, deputy_position = $9, deputy_phone = $10
            WHERE id = $11
            RETURNING *
        `, [
            manager_id, travel_country, notification_date, departure_date, return_date,
            travel_type, travel_purpose, deputy_name, deputy_position, deputy_phone, id
        ]);

        res.json({
            status: 'success',
            message: 'تم تحديث طلب السفر بنجاح',
            data: result.rows[0]
        });
    } catch (err) {
        console.error('خطأ في تحديث طلب السفر:', err);
        res.status(500).json({
            status: 'error',
            message: err.message
        });
    }
});

app.delete('/api/travel-requests/:id', async (req, res) => {
    try {
        const { id } = req.params;

        const requestCheck = await pool.query('SELECT id FROM travel_requests WHERE id = $1', [id]);
        if (requestCheck.rows.length === 0) {
            return res.status(404).json({
                status: 'error',
                message: 'طلب السفر غير موجود'
            });
        }

        await pool.query('DELETE FROM travel_requests WHERE id = $1', [id]);

        res.json({
            status: 'success',
            message: 'تم حذف طلب السفر بنجاح'
        });
    } catch (err) {
        console.error('خطأ في حذف طلب السفر:', err);
        res.status(500).json({
            status: 'error',
            message: err.message
        });
    }
});

// ===== API إدارة طلبات الإجازة =====

app.get('/api/vacation-requests', async (req, res) => {
    try {
        console.log('طلب جلب طلبات الإجازة مع الفلترة:', req.query);
        const filters = req.query;
        const result = await db.getVacationRequests(filters);
        console.log('تم جلب طلبات الإجازة:', result.length);
        res.json({
            status: 'success',
            data: result
        });
    } catch (err) {
        console.error('خطأ في جلب طلبات الإجازة:', err);
        res.status(500).json({
            status: 'error',
            message: err.message
        });
    }
});

app.get('/api/vacation-requests/:id', async (req, res) => {
    try {
        const { id } = req.params;
        console.log(`جلب تفاصيل طلب الإج��زة رقم: ${id}`);

        const result = await pool.query(`
            SELECT vr.*, m.full_name as manager_name, m.position as manager_position, m.department as manager_department
            FROM vacation_requests vr
            JOIN managers m ON vr.manager_id = m.id
            WHERE vr.id = $1
        `, [id]);

        if (result.rows.length === 0) {
            console.log(`لم يتم العثور على طلب الإجازة رقم: ${id}`);
            return res.status(404).json({
                status: 'error',
                message: 'طلب الإجازة غير موجود'
            });
        }

        console.log(`تم العثور على طلب الإجازة رقم: ${id}`);
        res.json({
            status: 'success',
            data: result.rows[0]
        });
    } catch (err) {
        console.error('خطأ في جلب طلب الإجازة:', err);
        res.status(500).json({
            status: 'error',
            message: err.message
        });
    }
});

app.post('/api/vacation-requests', async (req, res) => {
    try {
        const {
            manager_id, vacation_type, duration, start_date, end_date,
            deputy_name, deputy_position, deputy_phone
        } = req.body;

        const managerCheck = await pool.query('SELECT id FROM managers WHERE id = $1', [manager_id]);
        if (managerCheck.rows.length === 0) {
            return res.status(400).json({
                status: 'error',
                message: 'المدير غير موجود'
            });
        }

        const result = await pool.query(`
            INSERT INTO vacation_requests (
                manager_id, vacation_type, duration, start_date, end_date,
                deputy_name, deputy_position, deputy_phone
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING *
        `, [
            manager_id, vacation_type, duration, start_date, end_date,
            deputy_name, deputy_position, deputy_phone
        ]);

        res.status(201).json({
            status: 'success',
            message: 'تم إضافة طلب الإجازة بنجاح',
            data: result.rows[0]
        });
    } catch (err) {
        console.error('خطأ في إضافة طلب الإجازة:', err);
        res.status(500).json({
            status: 'error',
            message: err.message
        });
    }
});

app.put('/api/vacation-requests/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const {
            manager_id, vacation_type, duration, start_date, end_date,
            deputy_name, deputy_position, deputy_phone
        } = req.body;

        const requestCheck = await pool.query('SELECT id FROM vacation_requests WHERE id = $1', [id]);
        if (requestCheck.rows.length === 0) {
            return res.status(404).json({
                status: 'error',
                message: 'طلب الإجازة غير موجود'
            });
        }

        const result = await pool.query(`
            UPDATE vacation_requests SET
                manager_id = $1, vacation_type = $2, duration = $3, start_date = $4, end_date = $5,
                deputy_name = $6, deputy_position = $7, deputy_phone = $8
            WHERE id = $9
            RETURNING *
        `, [
            manager_id, vacation_type, duration, start_date, end_date,
            deputy_name, deputy_position, deputy_phone, id
        ]);

        res.json({
            status: 'success',
            message: 'تم تحديث طلب الإجازة بنجاح',
            data: result.rows[0]
        });
    } catch (err) {
        console.error('خطأ في تحديث طلب الإجازة:', err);
        res.status(500).json({
            status: 'error',
            message: err.message
        });
    }
});

app.delete('/api/vacation-requests/:id', async (req, res) => {
    try {
        const { id } = req.params;

        const requestCheck = await pool.query('SELECT id FROM vacation_requests WHERE id = $1', [id]);
        if (requestCheck.rows.length === 0) {
            return res.status(404).json({
                status: 'error',
                message: 'طلب الإجازة غير موجود'
            });
        }

        await pool.query('DELETE FROM vacation_requests WHERE id = $1', [id]);

        res.json({
            status: 'success',
            message: 'تم حذف طلب الإجازة بنجاح'
        });
    } catch (err) {
        console.error('خطأ في حذف طلب الإجازة:', err);
        res.status(500).json({
            status: 'error',
            message: err.message
        });
    }
});

// ===== API الإحصائيات والتقارير =====

app.get('/api/currently-traveling-managers', async (req, res) => {
    try {
        console.log('طلب الحصول على المدراء المسافرين حاليا');
        const travelingManagers = await db.getCurrentlyTravelingManagers();
        console.log(`تم العثور على ${travelingManagers.length} مدير مسافر حاليا`);
        res.json({
            success: true,
            data: travelingManagers
        });
    } catch (err) {
        console.error('خطأ في جلب المدراء المسافرين حاليا:', err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
});

app.get('/api/currently-vacationing-managers', async (req, res) => {
    try {
        console.log('طلب الحصول على المدراء في إجازة حاليا');
        const vacationingManagers = await db.getCurrentlyVacationingManagers();
        console.log(`تم العثور على ${vacationingManagers.length} مدير في إجازة حاليا`);
        res.json({
            success: true,
            data: vacationingManagers
        });
    } catch (err) {
        console.error('خطأ في جلب المدراء في إجازة حاليا:', err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
});

app.get('/api/currently-absent-managers', async (req, res) => {
    try {
        console.log('طلب الحصول على جميع المدراء الغائبين حاليا');
        const absentManagers = await db.getCurrentlyAbsentManagers();
        console.log(`تم العثور على ${absentManagers.length} مدير غائب حاليا`);
        res.json({
            success: true,
            data: absentManagers
        });
    } catch (err) {
        console.error('خطأ في جلب المدراء الغائبين حاليا:', err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
});

app.get('/api/most-traveling-managers', async (req, res) => {
    try {
        console.log('طلب الحصول على المدراء الأكثر سفراً');
        const limit = parseInt(req.query.limit) || 5;
        const managers = await db.getMostTravelingManagers(limit);
        console.log('المدراء الأكثر سفراً:', managers.length);
        res.json({
            success: true,
            data: managers
        });
    } catch (err) {
        console.error('خطأ في جلب بيانات المدراء الأكثر سفراً:', err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
});

app.get('/api/most-vacationing-managers', async (req, res) => {
    try {
        console.log('طلب الحصول على المدراء الأكثر إجازةً');
        const limit = parseInt(req.query.limit) || 5;
        const managers = await db.getMostVacationingManagers(limit);
        console.log('المدراء الأكثر إجازةً:', managers.length);
        res.json({
            success: true,
            data: managers
        });
    } catch (err) {
        console.error('خطأ في جلب بيانات المدراء الأكثر إجازةً:', err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
});

app.get('/api/travel-requests/stats', async (req, res) => {
    try {
        console.log('طلب الحصول على إحصائيات طلبات السفر');
        const stats = await db.getTravelRequestsStats();
        console.log('إحصائيات طلبات السفر:', stats);
        res.json({
            success: true,
            data: stats
        });
    } catch (err) {
        console.error('خطأ في جلب إحصائيات طلبات السفر:', err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
});

app.get('/api/vacation-requests/stats', async (req, res) => {
    try {
        console.log('طلب الحصول على إحصائيات طلبات الإجازة');
        const stats = await db.getVacationRequestsStats();
        console.log('إحصائيات طلبات الإجازة:', stats);
        res.json({
            success: true,
            data: stats
        });
    } catch (err) {
        console.error('خطأ في جلب إحصائيات طلبات الإجازة:', err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
});

app.get('/api/managers/stats', async (req, res) => {
    try {
        console.log('طلب الحصول على إحصائيات المدراء');
        const stats = await db.getManagersStats();
        console.log('إحصائيات المدراء:', stats);
        res.json({
            success: true,
            data: stats
        });
    } catch (err) {
        console.error('خطأ في جلب إحصائيات المدراء:', err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
});

app.get('/api/dashboard/stats', async (req, res) => {
    try {
        console.log('طلب الحصول على إحصائيات لوحة المعلومات');
        const dashboardStats = await db.getDashboardStats();
        console.log('إحصائيات لوحة المعلومات:', dashboardStats);
        res.json({
            success: true,
            data: dashboardStats
        });
    } catch (err) {
        console.error('خطأ في جلب إحصائيات لوحة المعلومات:', err);
        res.status(500).json({
            success: false,
            message: err.message
        });
    }
});

// ===== API إدارة المستخدمين =====

// استيراد وحدة API للمستخ��مين
try {
    const userApi = require('./api-users');
    userApi.setupUserRoutes(app, db);
    console.log('تم تحميل وحدة API للمستخدمين بنجاح');
} catch (err) {
    console.log('لم يتم العثور على وحدة API للمستخدمين، تخطي تحميلها');
}

app.post('/create-users-table', async (req, res) => {
    try {
        console.log('إنشاء جدول المستخدمين...');

        await pool.query(`
            CREATE TABLE IF NOT EXISTS users (
                id SERIAL PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                email VARCHAR(100) UNIQUE,
                role VARCHAR(20) NOT NULL DEFAULT 'user',
                is_active BOOLEAN DEFAULT TRUE,
                last_login TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );

            CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
            CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);

            CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = CURRENT_TIMESTAMP;
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;

            DROP TRIGGER IF EXISTS update_users_updated_at ON users;
            CREATE TRIGGER update_users_updated_at
                BEFORE UPDATE ON users
                FOR EACH ROW
                EXECUTE FUNCTION update_updated_at_column();
        `);

        console.log('تم إنشاء جدول المستخدمين بنجاح');

        const hashedPassword = await bcrypt.hash('admin', 10);
        await pool.query(`
            INSERT INTO users (username, password, full_name, email, role, is_active)
            VALUES ('admin', $1, 'مدير النظام', '<EMAIL>', 'admin', true)
            ON CONFLICT (username) DO NOTHING;
        `, [hashedPassword]);

        console.log('تم إضافة المستخدم الافتراضي بنجاح');

        res.json({
            status: 'success',
            message: 'تم إنشاء جدول المستخدمين وإضافة المستخدم الافتراضي بنجاح'
        });
    } catch (err) {
        console.error('خطأ في إنشاء جدول المستخدمين:', err);
        res.status(500).json({
            status: 'error',
            message: 'حدث خطأ أثناء إنشاء جدول المستخدمين: ' + err.message
        });
    }
});

app.get('/get-users', async (req, res) => {
    try {
        console.log('محاولة جلب بيانات المستخدمين من قاعدة البيانات...');

        const tableCheck = await pool.query(`
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'users'
            );
        `);

        const tableExists = tableCheck.rows[0].exists;
        console.log('هل جدول المستخدمين موجود؟', tableExists);

        if (!tableExists) {
            console.log('جدول المستخدمين غير موجود، محاولة إنشائه...');
            
            try {
                await pool.query(`
                    CREATE TABLE IF NOT EXISTS users (
                        id SERIAL PRIMARY KEY,
                        username VARCHAR(50) UNIQUE NOT NULL,
                        password VARCHAR(100) NOT NULL,
                        full_name VARCHAR(100) NOT NULL,
                        email VARCHAR(100) UNIQUE,
                        role VARCHAR(20) DEFAULT 'user',
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    );
                `);
                
                console.log('تم إنشاء جدول المستخدمين بنجاح');
            } catch (createError) {
                console.error('خطأ في إنشاء جدول المستخدمين:', createError);
                return res.status(500).json({
                    success: false,
                    message: 'حدث خطأ في إنشاء جدول المستخدمين',
                    error: createError.message
                });
            }
        }

        try {
            const users = await db.getAllUsers();
            console.log(`تم جلب ${users.length} مستخدم من قاعدة البيانات باستخدام مكتبة db`);
            if (users && users.length > 0) {
                return res.json(users);
            }
        } catch (dbError) {
            console.error('خطأ في جلب المستخدمين باستخدام مكتبة db:', dbError);
        }

        const usersResult = await pool.query(`
            SELECT id, username, full_name, email, role, is_active, created_at 
            FROM users 
            ORDER BY id
        `);
        
        console.log(`ت�� جلب ${usersResult.rows.length} مستخدم من قاعدة البيانات مباشرة`);

        const formattedUsers = usersResult.rows.map(user => ({
            id: user.id,
            username: user.username,
            full_name: user.full_name,
            email: user.email || '',
            role: user.role || 'user',
            is_active: user.is_active === true || user.is_active === 'true',
            created_at: user.created_at
        }));

        return res.json(formattedUsers);
    } catch (error) {
        console.error('خطأ في جلب بيانات المستخدمين:', error);
        res.status(500).json({
            success: false,
            message: 'حدث خطأ في جلب بيانات المستخدمين',
            error: error.message
        });
    }
});

app.post('/direct-users', async (req, res) => {
    try {
        console.log('طلب إضافة مستخدم جديد مباشرة:', req.body);
        const { username, password, full_name, email, role, is_active } = req.body;

        if (!username || !password || !full_name) {
            console.log('بيانات غير مكتملة:', { username, password: password ? 'موجود' : 'غير موجود', full_name });
            return res.status(400).json({
                status: 'error',
                message: 'يرجى إدخال اسم المستخدم وكلمة المرور والاسم الكامل'
            });
        }

        const hashedPassword = await bcrypt.hash(password, 10);
        console.log('تم تشفير كلمة المرور بنجاح');

        const newUser = await db.createUser({
            username,
            password: hashedPassword,
            full_name,
            email,
            role: role || 'user',
            is_active: is_active !== undefined ? is_active : true
        });

        console.log('تم إنشاء المستخدم بنجاح:', newUser);

        res.status(201).json({
            status: 'success',
            message: 'تم إنشاء المستخدم بنجاح',
            data: {
                id: newUser.id,
                username: newUser.username,
                full_name: newUser.full_name,
                email: newUser.email,
                role: newUser.role,
                is_active: newUser.is_active,
                created_at: newUser.created_at
            }
        });
    } catch (err) {
        console.error('خطأ في إنشاء مستخدم جديد:', err);
        console.error('تفاصيل الخطأ:', err.message);

        if (err.code === '23505') {
            return res.status(400).json({
                status: 'error',
                message: 'اسم المستخدم أو البريد الإلكتروني مستخدم بالفعل'
            });
        }

        res.status(500).json({
            status: 'error',
            message: 'حدث خطأ أثناء إنشاء المستخدم الجديد: ' + err.message
        });
    }
});

// ===== نقاط نهاية اختبار =====

app.get('/test', (req, res) => {
    res.json({ message: 'Server is working!' });
});

app.get('/api/test', async (req, res) => {
    try {
        const result = await pool.query('SELECT NOW()');
        res.json({ status: 'success', time: result.rows[0].now });
    } catch (err) {
        res.status(500).json({ status: 'error', message: err.message });
    }
});

// ===== تشغيل الخادم =====

const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
    console.log(`تم تشغيل الخادم بنجاح على المنفذ ${PORT}`);
    console.log('المسار الحالي للتطبيق:', __dirname);
    console.log(`يمكن الوصول إلى التطبيق عبر: http://localhost:${PORT}`);

    console.log('بدء اختبار الاتصال بقاعدة البيانات...');

    try {
        console.log('تم استيراد وحدة db.js بنجاح');
        console.log('محاولة الاتصال بقاعدة البيانات...');

        if (typeof db.testConnection !== 'function') {
            console.error('خطأ: دالة testConnection غير موجودة في وحدة db.js');
            console.log('الدوال المتاحة في وحدة db.js:', Object.keys(db));
            return;
        }

        db.testConnection()
            .then((result) => {
                console.log('تم الاتصال بقاعدة البيانات بنجاح');
                console.log('نتيجة اختبار الاتصال:', result);

                if (typeof db.checkTableExists !== 'function') {
                    console.error('خطأ: دالة checkTableExists غير موجودة في وحدة db.js');
                    return;
                }

                Promise.all([
                    db.checkTableExists('managers'),
                    db.checkTableExists('travel_requests'),
                    db.checkTableExists('vacation_requests')
                ])
                .then(results => {
                    console.log('نتائج التحقق من الجداول:', results);
                    const [managersExists, travelRequestsExists, vacationRequestsExists] = results;

                    if (!managersExists || !travelRequestsExists || !vacationRequestsExists) {
                        console.warn('بعض الجداول الأساسية غير موجودة في قاعدة البيانات!');
                        console.warn('جدول المدراء موجود:', managersExists);
                        console.warn('جدول طلبات السفر موجود:', travelRequestsExists);
                        console.warn('جدول طلبات الإجازة موجود:', vacationRequestsExists);
                    } else {
                        console.log('جميع الجداول الأساسية موجودة في قاعدة البيانات');
                    }
                })
                .catch(err => {
                    console.error('خطأ في التحقق من وجود الجداول:', err);
                });
            })
            .catch(err => {
                console.error('فشل الاتصال بقاعدة البيانات:', err);
                console.error('تفاصيل الخطأ:', err.message);
            });
    } catch (err) {
        console.error('خطأ أثناء محاولة الاتصال بقاعدة البيانات:', err);
        console.error('تتبع الخطأ:', err.stack);
    }
});